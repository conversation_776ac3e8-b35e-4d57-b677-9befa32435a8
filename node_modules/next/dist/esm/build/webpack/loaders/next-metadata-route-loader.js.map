{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-metadata-route-loader.ts"], "names": ["fs", "path", "imageExtMimeTypeMap", "getLoaderModuleNamedExports", "errorOnBadHandler", "resourcePath", "JSON", "stringify", "createReExportsCode", "loaderContext", "exportNames", "reExportNames", "filter", "name", "length", "join", "cacheHeader", "none", "longCache", "revalidate", "getFilenameAndExtension", "filename", "basename", "ext", "split", "getContentType", "getStaticAssetRouteCode", "fileBaseName", "cache", "process", "env", "NODE_ENV", "code", "promises", "readFile", "toString", "getDynamicTextRouteCode", "getDynamicImageRouteCode", "getDynamicSiteMapRouteCode", "page", "staticGenerationCode", "hasGenerateSiteMaps", "includes", "nextMetadataRouterLoader", "isDynamic", "filePath", "getOptions", "addDependency"], "mappings": "AACA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,SAASC,mBAAmB,QAAQ,yBAAwB;AAC5D,SAASC,2BAA2B,QAAQ,UAAS;AAErD,SAASC,kBAAkBC,YAAoB;IAC7C,OAAO,CAAC;;kDAEwC,EAAEC,KAAKC,SAAS,CAC5DF,cACA;;EAEJ,CAAC;AACH;AAEA,wCAAwC,GACxC,eAAeG,oBACbH,YAAoB,EACpBI,aAAyC;IAEzC,MAAMC,cAAc,MAAMP,4BACxBE,cACAI;IAEF,iDAAiD;IACjD,MAAME,gBAAgBD,YAAYE,MAAM,CACtC,CAACC,OAASA,SAAS,aAAaA,SAAS;IAG3C,OAAOF,cAAcG,MAAM,GAAG,IAC1B,CAAC,SAAS,EAAEH,cAAcI,IAAI,CAAC,MAAM,QAAQ,EAAET,KAAKC,SAAS,CAC3DF,cACA,EAAE,CAAC,GACL;AACN;AAEA,MAAMW,cAAc;IAClBC,MAAM;IACNC,WAAW;IACXC,YAAY;AACd;AAUA,OAAO,SAASC,wBAAwBf,YAAoB;IAC1D,MAAMgB,WAAWpB,KAAKqB,QAAQ,CAACjB;IAC/B,MAAM,CAACQ,MAAMU,IAAI,GAAGF,SAASG,KAAK,CAAC,KAAK;IACxC,OAAO;QAAEX;QAAMU;IAAI;AACrB;AAEA,SAASE,eAAepB,YAAoB;IAC1C,IAAI,EAAEQ,IAAI,EAAEU,GAAG,EAAE,GAAGH,wBAAwBf;IAC5C,IAAIkB,QAAQ,OAAOA,MAAM;IAEzB,IAAIV,SAAS,aAAaU,QAAQ,OAAO,OAAO;IAChD,IAAIV,SAAS,WAAW,OAAO;IAC/B,IAAIA,SAAS,UAAU,OAAO;IAC9B,IAAIA,SAAS,YAAY,OAAO;IAEhC,IAAIU,QAAQ,SAASA,QAAQ,UAAUA,QAAQ,SAASA,QAAQ,OAAO;QACrE,OAAOrB,mBAAmB,CAACqB,IAAI;IACjC;IACA,OAAO;AACT;AAEA,eAAeG,wBACbrB,YAAoB,EACpBsB,YAAoB;IAEpB,MAAMC,QACJD,iBAAiB,YACb,uCACAE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzBf,YAAYC,IAAI,GAChBD,YAAYE,SAAS;IAC3B,MAAMc,OAAO,CAAC;;;;oBAII,EAAE1B,KAAKC,SAAS,CAACkB,eAAepB,eAAe;2BACxC,EAAEC,KAAKC,SAAS,CACvC,AAAC,CAAA,MAAMP,GAAGiC,QAAQ,CAACC,QAAQ,CAAC7B,aAAY,EAAG8B,QAAQ,CAAC,WACpD;;;;;;;uBAOmB,EAAE7B,KAAKC,SAAS,CAACqB,OAAO;;;;;;AAM/C,CAAC;IACC,OAAOI;AACT;AAEA,eAAeI,wBACb/B,YAAoB,EACpBI,aAAyC;IAEzC,OAAO,CAAC;;;oBAGU,EAAEH,KAAKC,SAAS,CAACF,cAAc;;;oBAG/B,EAAEC,KAAKC,SAAS,CAACkB,eAAepB,eAAe;iBAClD,EAAEC,KAAKC,SAAS,CAACa,wBAAwBf,cAAcQ,IAAI,EAAE;;AAE9E,EAAET,kBAAkBC,cAAc;AAClC,EAAE,MAAMG,oBAAoBH,cAAcI,eAAe;;;;;;;;;uBASlC,EAAEH,KAAKC,SAAS,CAACS,YAAYG,UAAU,EAAE;;;;AAIhE,CAAC;AACD;AAEA,iCAAiC;AACjC,eAAekB,yBACbhC,YAAoB,EACpBI,aAAyC;IAEzC,OAAO,CAAC;;;0BAGgB,EAAEH,KAAKC,SAAS,CAACF,cAAc;;;;;;;AAOzD,EAAED,kBAAkBC,cAAc;AAClC,EAAE,MAAMG,oBAAoBH,cAAcI,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;AAyBzD,CAAC;AACD;AAEA,eAAe6B,2BACbjC,YAAoB,EACpBkC,IAAY,EACZ9B,aAAyC;IAEzC,IAAI+B,uBAAuB;IAE3B,MAAM9B,cAAc,MAAMP,4BACxBE,cACAI;IAGF,MAAMgC,sBAAsB/B,YAAYgC,QAAQ,CAAC;IACjD,IACEb,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBU,uBACAF,KAAKG,QAAQ,CAAC,sBACd;QACAF,uBAAuB,CAAC;;;;;;;;;;;IAWxB,CAAC;IACH;IAEA,MAAMR,OAAO,CAAC;;0BAEU,EAAE1B,KAAKC,SAAS,CAACF,cAAc;;;;;;oBAMrC,EAAEC,KAAKC,SAAS,CAACkB,eAAepB,eAAe;iBAClD,EAAEC,KAAKC,SAAS,CAACa,wBAAwBf,cAAcQ,IAAI,EAAE;;AAE9E,EAAET,kBAAkBC,cAAc;AAClC,EAAE,MAAMG,oBAAoBH,cAAcI,eAAe;;;;EAIvD,EACE,GAAG,2FAA2F,IAC/F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAkCoB,EAAEH,KAAKC,SAAS,CAACS,YAAYG,UAAU,EAAE;;;;;AAKhE,EAAEqB,qBAAqB;AACvB,CAAC;IACC,OAAOR;AACT;AAEA,gFAAgF;AAChF,oDAAoD;AACpD,MAAMW,2BACJ;IACE,MAAM,EAAEJ,IAAI,EAAEK,SAAS,EAAEC,QAAQ,EAAE,GAAG,IAAI,CAACC,UAAU;IACrD,MAAM,EAAEjC,MAAMc,YAAY,EAAE,GAAGP,wBAAwByB;IACvD,IAAI,CAACE,aAAa,CAACF;IAEnB,IAAIb,OAAO;IACX,IAAIY,cAAc,KAAK;QACrB,IAAIjB,iBAAiB,YAAYA,iBAAiB,YAAY;YAC5DK,OAAO,MAAMI,wBAAwBS,UAAU,IAAI;QACrD,OAAO,IAAIlB,iBAAiB,WAAW;YACrCK,OAAO,MAAMM,2BAA2BO,UAAUN,MAAM,IAAI;QAC9D,OAAO;YACLP,OAAO,MAAMK,yBAAyBQ,UAAU,IAAI;QACtD;IACF,OAAO;QACLb,OAAO,MAAMN,wBAAwBmB,UAAUlB;IACjD;IAEA,OAAOK;AACT;AAEF,eAAeW,yBAAwB"}