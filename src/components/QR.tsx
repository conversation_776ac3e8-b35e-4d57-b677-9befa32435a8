'use client'
import QRCode from 'qrcode'
import { useEffect, useRef } from 'react'

export default function QR({ text, size = 256 }: { text: string; size?: number }) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  useEffect(() => {
    if (!canvasRef.current) return
    QRCode.toCanvas(canvasRef.current, text, { width: size, margin: 1 })
  }, [text, size])
  return <canvas ref={canvasRef} className="rounded-lg shadow-lg bg-white" />
}