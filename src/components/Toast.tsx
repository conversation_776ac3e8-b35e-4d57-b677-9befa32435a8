'use client'
import { useEffect, useState } from 'react'

export function Toast({ text }: { text: string }) {
  const [show, setShow] = useState(true)
  useEffect(() => {
    const t = setTimeout(() => setShow(false), 2500)
    return () => clearTimeout(t)
  }, [])
  if (!show) return null
  return (
    <div className="fixed bottom-6 left-1/2 -translate-x-1/2 rounded-xl bg-black/70 px-4 py-2 text-sm shadow-lg">
      {text}
    </div>
  )
}