import React from 'react'
import { Message as M } from '@/lib/types'

function getRelativeTime(timestamp: number): string {
  const now = Date.now()
  const diff = now - timestamp
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (seconds < 60) return 'just now'
  if (minutes < 60) return `${minutes}m ago`
  if (hours < 24) return `${hours}h ago`
  if (days < 7) return `${days}d ago`
  return new Date(timestamp).toLocaleDateString()
}

function highlightText(text: string, searchQuery: string): React.ReactNode {
  if (!searchQuery) return text

  const regex = new RegExp(`(${searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  const parts = text.split(regex)

  return parts.map((part, index) =>
    regex.test(part) ? (
      <mark key={index} className="bg-yellow-400/30 text-yellow-200 px-1 rounded">
        {part}
      </mark>
    ) : part
  )
}

const REACTION_EMOJIS = ['👍', '❤️', '😂', '😮', '😢', '😡']

const Message = React.memo(function Message({
  msg,
  isNew = false,
  searchQuery = '',
  onReaction
}: {
  msg: M;
  isNew?: boolean;
  searchQuery?: string;
  onReaction?: (messageId: string, emoji: string) => void;
}) {
  const date = new Date(msg.ts)
  const relativeTime = getRelativeTime(msg.ts)

  return (
    <div className={`rounded-xl bg-white/10 px-4 py-2 backdrop-blur-sm transition-all duration-500 group ${
      isNew ? 'animate-slide-in-left' : ''
    }`}>
      <div
        className="text-xs opacity-70 cursor-help"
        title={date.toLocaleString()}
      >
        {relativeTime}
      </div>
      <div className="font-semibold">{highlightText(msg.name, searchQuery)}</div>
      <div className="opacity-90 mb-2">{highlightText(msg.text, searchQuery)}</div>

      {/* Reactions */}
      <div className="flex items-center gap-2 flex-wrap">
        {/* Existing reactions */}
        {msg.reactions && Object.entries(msg.reactions).map(([emoji, users]) => (
          <button
            key={emoji}
            onClick={() => onReaction?.(msg.id, emoji)}
            className="flex items-center gap-1 px-2 py-1 bg-white/10 hover:bg-white/20 rounded-full text-xs transition-colors"
          >
            <span>{emoji}</span>
            <span>{users.length}</span>
          </button>
        ))}

        {/* Add reaction button (visible on hover) */}
        {onReaction && (
          <div className="relative opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="group">
              <button className="px-2 py-1 bg-white/10 hover:bg-white/20 rounded-full text-xs">
                +
              </button>
              <div className="absolute bottom-full left-0 mb-1 hidden group-hover:flex bg-black/90 rounded-lg p-2 gap-1 z-10 shadow-lg">
                {REACTION_EMOJIS.map(emoji => (
                  <button
                    key={emoji}
                    onClick={(e) => {
                      e.stopPropagation()
                      onReaction(msg.id, emoji)
                    }}
                    className="hover:bg-white/20 rounded p-1 text-sm transition-colors"
                    title={`React with ${emoji}`}
                  >
                    {emoji}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
})

export default Message