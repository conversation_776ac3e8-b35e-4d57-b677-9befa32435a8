'use client'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

function randomRoom() {
  return Math.random().toString(36).slice(2, 8).toUpperCase()
}

export default function Home() {
  const r = useRouter()
  const [room, setRoom] = useState('')

  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-bold">Realtime QR Room</h1>
      <div className="grid gap-6 md:grid-cols-2">
        <div className="rounded-2xl bg-white/10 p-6">
          <h2 className="mb-3 text-xl font-semibold">Create a Room</h2>
          <button
            onClick={() => r.push(`/server/${randomRoom()}`)}
            className="rounded-xl bg-brand-500 px-5 py-3 font-semibold text-white hover:bg-brand-600"
          >
            New Server View
          </button>
        </div>
        <div className="rounded-2xl bg-white/10 p-6">
          <h2 className="mb-3 text-xl font-semibold">Join a Room</h2>
          <form
            className="space-y-3"
            onSubmit={(e) => {
              e.preventDefault()
              if (!room) return
              r.push(`/room/${room.toUpperCase()}`)
            }}
          >
            <input
              value={room}
              onChange={(e) => setRoom(e.target.value)}
              placeholder="Enter code (e.g., 7A2KQ1)"
              className="w-full rounded-lg bg-white/10 px-3 py-2 outline-none ring-1 ring-white/20"
            />
            <button className="rounded-xl bg-white/20 px-4 py-2">Join</button>
          </form>
        </div>
      </div>
    </div>
  )
}