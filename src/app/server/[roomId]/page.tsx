
'use client'
import { useEffect, useMemo, useRef, useState, useCallback } from 'react'
import io, { Socket } from 'socket.io-client'
import QR from '@/components/QR'
import Message from '@/components/Message'
import type { Message as M, Participant } from '@/lib/types'

export default function ServerView({ params }: { params: { roomId: string } }) {
  const { roomId } = params
  const [participants, setParticipants] = useState<Participant[]>([])
  const [messages, setMessages] = useState<M[]>([])
  const [hideNames, setHideNames] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting')
  const [soundEnabled, setSoundEnabled] = useState(true)
  const [autoScroll, setAutoScroll] = useState(true)
  const [selectedParticipants, setSelectedParticipants] = useState<Set<string>>(new Set())
  const [searchQuery, setSearchQuery] = useState('')
  const [showStats, setShowStats] = useState(false)
  const [newMessageIds, setNewMessageIds] = useState<Set<string>>(new Set())
  const [roomStartTime] = useState(Date.now())
  const [participantActivity, setParticipantActivity] = useState<Record<string, { lastSeen: number; joinTime: number }>>({})
  const [showParticipantEvents, setShowParticipantEvents] = useState(true)
  const [showSettings, setShowSettings] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [reconnectAttempts, setReconnectAttempts] = useState(0)
  const [settings, setSettings] = useState({
    maxMessages: 100,
    soundEnabled: true,
    autoScroll: true,
    showTimestamps: true,
    compactMode: false
  })
  const sockRef = useRef<Socket | null>(null)
  const scrollRef = useRef<HTMLDivElement>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  // Virtual scrolling state
  const [scrollTop, setScrollTop] = useState(0)
  const [containerHeight, setContainerHeight] = useState(0)
  const ITEM_HEIGHT = 120 // Approximate height of each message
  const BUFFER_SIZE = 5 // Number of items to render outside visible area

  const MAX_MESSAGES = 100 // Limit messages to prevent memory issues

  // Initialize audio for notifications
  useEffect(() => {
    // Create a simple notification sound using Web Audio API
    const createNotificationSound = () => {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1)

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2)

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.2)
    }

    audioRef.current = { play: createNotificationSound } as any
  }, [])

  const playNotificationSound = () => {
    if (soundEnabled && audioRef.current) {
      try {
        audioRef.current.play()
      } catch (error) {
        console.log('Could not play notification sound:', error)
      }
    }
  }

  // init socket inside component to avoid stale global instance
  useEffect(() => {
    const s = io(window.location.origin, {
      transports: ['websocket'],
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      timeout: 10000
    })
    sockRef.current = s

    const join = () => {
      try {
        s.emit('room:join', { roomId, name: 'Server' })
        setError(null)
      } catch (err) {
        setError('Failed to join room')
        console.error('Join error:', err)
      }
    }

    s.on('connect', () => {
      setConnectionStatus('connected')
      setReconnectAttempts(0)
      setError(null)
      join()
    })

    s.on('disconnect', (reason) => {
      setConnectionStatus('disconnected')
      if (reason === 'io server disconnect') {
        setError('Server disconnected the connection')
      } else {
        setError('Connection lost - attempting to reconnect...')
      }
    })

    s.on('connect_error', (err) => {
      setConnectionStatus('disconnected')
      setReconnectAttempts(prev => prev + 1)
      setError(`Connection failed: ${err.message || 'Unknown error'}`)
      console.error('Connection error:', err)
    })

    s.on('reconnect', (attemptNumber) => {
      setConnectionStatus('connected')
      setReconnectAttempts(0)
      setError(null)
      console.log(`Reconnected after ${attemptNumber} attempts`)
    })

    s.on('reconnect_error', (err) => {
      setError(`Reconnection failed: ${err.message || 'Unknown error'}`)
      console.error('Reconnection error:', err)
    })

    s.on('reconnect_failed', () => {
      setError('Failed to reconnect after maximum attempts')
      setConnectionStatus('disconnected')
    })

    join() // first load

    s.on('room:state', (state: { participants: Participant[]; messages: M[] }) => {
      try {
        if (!state || !Array.isArray(state.participants) || !Array.isArray(state.messages)) {
          throw new Error('Invalid room state received')
        }

        setParticipants(state.participants)
        setMessages(state.messages.slice(-MAX_MESSAGES)) // Keep only latest messages

        // Track participant activity
        const now = Date.now()
        setParticipantActivity(prev => {
          const updated = { ...prev }
          state.participants.forEach(p => {
            if (!p || !p.name) return // Skip invalid participants
            if (!updated[p.name]) {
              updated[p.name] = { joinTime: now, lastSeen: now }
            } else {
              updated[p.name].lastSeen = now
            }
          })
          return updated
        })
        setError(null) // Clear any previous errors
      } catch (err) {
        setError(`Failed to process room state: ${err instanceof Error ? err.message : 'Unknown error'}`)
        console.error('Room state error:', err)
      }
    })
    s.on('room:reaction', (data: { messageId: string; emoji: string; name: string }) => {
      try {
        if (!data || !data.messageId || !data.emoji || !data.name) {
          throw new Error('Invalid reaction data received')
        }

        setMessages(prev => prev.map(msg => {
          if (msg.id === data.messageId) {
            const reactions = { ...msg.reactions }
            if (!reactions[data.emoji]) {
              reactions[data.emoji] = []
            }
            // Toggle reaction - remove if already exists, add if not
            const userIndex = reactions[data.emoji].indexOf(data.name)
            if (userIndex > -1) {
              reactions[data.emoji].splice(userIndex, 1)
              if (reactions[data.emoji].length === 0) {
                delete reactions[data.emoji]
              }
            } else {
              reactions[data.emoji].push(data.name)
            }
            return { ...msg, reactions }
          }
          return msg
        }))
      } catch (err) {
        setError(`Failed to process reaction: ${err instanceof Error ? err.message : 'Unknown error'}`)
        console.error('Reaction error:', err)
      }
    })

    s.on('room:message', (msg: M) => {
      try {
        if (!msg || !msg.id || !msg.name || typeof msg.text !== 'string' || !msg.ts) {
          throw new Error('Invalid message data received')
        }

        setMessages((prev) => {
          const newMessages = [...prev, msg]
          return newMessages.slice(-MAX_MESSAGES) // Auto-cleanup old messages
        })

        // Update participant activity
        setParticipantActivity(prev => ({
          ...prev,
          [msg.name]: {
            ...prev[msg.name],
            lastSeen: Date.now(),
            joinTime: prev[msg.name]?.joinTime || Date.now()
          }
        }))

        // Mark message as new for animation
        setNewMessageIds(prev => new Set([...prev, msg.id]))
        // Remove the new flag after animation completes
        setTimeout(() => {
          setNewMessageIds(prev => {
            const newSet = new Set(prev)
            newSet.delete(msg.id)
            return newSet
          })
        }, 500)
        // Play notification sound for new messages (but not from Server)
        if (msg.name !== 'Server') {
          playNotificationSound()
        }
      } catch (err) {
        setError(`Failed to process message: ${err instanceof Error ? err.message : 'Unknown error'}`)
        console.error('Message error:', err)
      }
    })

    return () => {
      s.off('connect')
      s.off('disconnect')
      s.off('connect_error')
      s.off('room:state')
      s.off('room:message')
      s.off('room:reaction')
      s.disconnect()
    }
  }, [roomId])

  // auto-scroll on new messages (only if enabled)
  useEffect(() => {
    if (autoScroll) {
      const el = scrollRef.current
      if (el) el.scrollTop = el.scrollHeight
    }
  }, [messages, autoScroll])



  // Detect manual scrolling to disable auto-scroll and update virtual scroll
  const handleScroll = useCallback(() => {
    const el = scrollRef.current
    if (el) {
      const newScrollTop = el.scrollTop
      const newContainerHeight = el.clientHeight

      setScrollTop(newScrollTop)
      setContainerHeight(newContainerHeight)

      if (autoScroll) {
        const isAtBottom = el.scrollHeight - newScrollTop <= newContainerHeight + 50 // 50px threshold
        if (!isAtBottom) {
          setAutoScroll(false)
        }
      }
    }
  }, [autoScroll])

  // Update container height on mount
  useEffect(() => {
    const el = scrollRef.current
    if (el) {
      setContainerHeight(el.clientHeight)
      const resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          setContainerHeight(entry.contentRect.height)
        }
      })
      resizeObserver.observe(el)
      return () => resizeObserver.disconnect()
    }
  }, [])

  const joinUrl = useMemo(() => {
    const base = process.env.NEXT_PUBLIC_BASE_URL || window.location.origin
    return `${base}/room/${roomId}`
  }, [roomId])

  const clearMessages = useCallback(() => {
    try {
      sockRef.current?.emit('room:clear', { roomId })
    } catch (err) {
      setError('Failed to clear messages')
      console.error('Clear messages error:', err)
    }
  }, [roomId])

  const manualReconnect = useCallback(() => {
    try {
      setError(null)
      setConnectionStatus('connecting')
      sockRef.current?.disconnect()
      sockRef.current?.connect()
    } catch (err) {
      setError('Failed to reconnect manually')
      console.error('Manual reconnect error:', err)
    }
  }, [])

  const dismissError = useCallback(() => setError(null), [])

  const handleReaction = useCallback((messageId: string, emoji: string) => {
    try {
      sockRef.current?.emit('room:reaction', {
        roomId,
        messageId,
        emoji,
        name: 'Server' // Server can react to messages
      })
    } catch (err) {
      setError('Failed to send reaction')
      console.error('Reaction error:', err)
    }
  }, [roomId])

  // Memoize expensive calculations
  const visible = useMemo(() =>
    participants.filter((p) => (p?.name ?? '').trim() && p.name !== 'Server'),
    [participants]
  )

  // Filter messages by selected participants and search query
  const filteredMessages = useMemo(() => messages.filter(m => {
    // Filter by participants
    const participantMatch = selectedParticipants.size === 0 || selectedParticipants.has(m.name)
    // Filter by search query
    const searchMatch = !searchQuery ||
      m.text.toLowerCase().includes(searchQuery.toLowerCase()) ||
      m.name.toLowerCase().includes(searchQuery.toLowerCase())
    return participantMatch && searchMatch
  }), [messages, selectedParticipants, searchQuery])

  // Virtual scrolling calculations
  const visibleMessages = useMemo(() => {
    if (filteredMessages.length <= 50) {
      // Don't use virtual scrolling for small lists
      return filteredMessages
    }

    const startIndex = Math.max(0, Math.floor(scrollTop / ITEM_HEIGHT) - BUFFER_SIZE)
    const endIndex = Math.min(
      filteredMessages.length,
      Math.ceil((scrollTop + containerHeight) / ITEM_HEIGHT) + BUFFER_SIZE
    )

    return filteredMessages.slice(startIndex, endIndex).map((msg, index) => ({
      ...msg,
      virtualIndex: startIndex + index
    }))
  }, [filteredMessages, scrollTop, containerHeight, ITEM_HEIGHT, BUFFER_SIZE])

  // Get unique participant names from messages
  const allParticipantNames = useMemo(() =>
    Array.from(new Set(messages.map(m => m.name))).sort(),
    [messages]
  )

  // Calculate statistics
  const stats = useMemo(() => {
    const activeTime = Math.floor((Date.now() - roomStartTime) / 1000 / 60) // minutes
    return {
      totalMessages: messages.length,
      totalParticipants: visible.length,
      activeTime,
      messagesByParticipant: allParticipantNames.reduce((acc, name) => {
        acc[name] = messages.filter(m => m.name === name).length
        return acc
      }, {} as Record<string, number>),
      averageMessageLength: messages.length > 0
        ? Math.round(messages.reduce((sum, m) => sum + m.text.length, 0) / messages.length)
        : 0,
      messagesPerMinute: activeTime > 0 ? (messages.length / activeTime).toFixed(1) : '0'
    }
  }, [messages, visible.length, allParticipantNames, roomStartTime])

  const toggleParticipantFilter = useCallback((name: string) => {
    setSelectedParticipants(prev => {
      const newSet = new Set(prev)
      if (newSet.has(name)) {
        newSet.delete(name)
      } else {
        newSet.add(name)
      }
      return newSet
    })
  }, [])

  const exportMessages = useCallback((format: 'json' | 'txt') => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const filename = `room-${roomId}-messages-${timestamp}.${format}`

    let content: string
    if (format === 'json') {
      content = JSON.stringify({
        roomId,
        exportedAt: new Date().toISOString(),
        messageCount: messages.length,
        messages: messages.map(m => ({
          ...m,
          timestamp: new Date(m.ts).toISOString()
        }))
      }, null, 2)
    } else {
      content = `Room: ${roomId}\nExported: ${new Date().toLocaleString()}\nMessages: ${messages.length}\n\n` +
        messages.map(m =>
          `[${new Date(m.ts).toLocaleString()}] ${m.name}: ${m.text}`
        ).join('\n')
    }

    const blob = new Blob([content], { type: format === 'json' ? 'application/json' : 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }, [roomId, messages])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) return

      switch (e.key.toLowerCase()) {
        case 'f':
          e.preventDefault()
          document.documentElement.requestFullscreen?.()
          break
        case 'c':
          e.preventDefault()
          clearMessages()
          break
        case 'h':
          e.preventDefault()
          setHideNames(v => !v)
          break
        case 's':
          e.preventDefault()
          setShowSettings(v => !v)
          break
        case 'escape':
          e.preventDefault()
          setShowSettings(false)
          setError(null)
          break
      }
    }

    document.addEventListener('keydown', handleKeyPress)
    return () => document.removeEventListener('keydown', handleKeyPress)
  }, [clearMessages])

  return (
    <div className="space-y-8 select-none" role="main" aria-label="Room server interface">
      {/* Error Banner */}
      {error && (
        <div
          className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 flex items-center justify-between"
          role="alert"
          aria-live="polite"
        >
          <div className="flex items-center gap-3">
            <div className="text-red-400" aria-hidden="true">⚠️</div>
            <div>
              <div className="font-semibold text-red-200">Connection Error</div>
              <div className="text-sm text-red-300">{error}</div>
              {reconnectAttempts > 0 && (
                <div className="text-xs text-red-400 mt-1">
                  Reconnection attempts: {reconnectAttempts}/5
                </div>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            {connectionStatus === 'disconnected' && (
              <button
                onClick={manualReconnect}
                className="px-3 py-1 bg-red-500/30 hover:bg-red-500/40 rounded text-sm"
                aria-label="Retry connection"
              >
                Retry
              </button>
            )}
            <button
              onClick={dismissError}
              className="px-3 py-1 bg-white/10 hover:bg-white/20 rounded text-sm"
              aria-label="Dismiss error message"
            >
              Dismiss
            </button>
          </div>
        </div>
      )}
      <div className="flex items-center justify-between gap-6">
        <div>
          <div className="flex items-center gap-2">
            <div className="text-sm uppercase tracking-[0.3em] opacity-80">Room</div>
            <div className={`h-2 w-2 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-400' :
              connectionStatus === 'connecting' ? 'bg-yellow-400 animate-pulse' :
              'bg-red-400'
            }`} title={`Connection: ${connectionStatus}`} />
          </div>
          <div className="font-black leading-none text-[clamp(2rem,10vw,8rem)]">{roomId}</div>
          <div className="opacity-80">Scan to join</div>
        </div>
        <div className="flex flex-col items-center gap-2">
          <QR text={joinUrl} size={260} />
          <div className="text-xs opacity-80 max-w-[260px] text-center break-all">{joinUrl}</div>
        </div>
      </div>

      {!hideNames && (
        <div>
          <div className="flex items-center justify-between mb-3">
            <div className="text-[clamp(1rem,3vw,2rem)] font-semibold">
              Participants ({visible.length})
            </div>
            <button
              onClick={() => setShowParticipantEvents(!showParticipantEvents)}
              className="text-sm bg-white/10 hover:bg-white/20 px-3 py-1 rounded-lg"
            >
              {showParticipantEvents ? 'Hide Activity' : 'Show Activity'}
            </button>
          </div>
          <ul className="grid gap-3 [grid-template-columns:repeat(auto-fit,minmax(16ch,1fr))]">
            {visible.map((p) => {
              const activity = participantActivity[p.name]
              const isActive = activity && (Date.now() - activity.lastSeen) < 60000 // Active within 1 minute
              const joinedAgo = activity ? Math.floor((Date.now() - activity.joinTime) / 1000 / 60) : 0
              const lastSeenAgo = activity ? Math.floor((Date.now() - activity.lastSeen) / 1000 / 60) : 0

              return (
                <li key={p.id} className="rounded-xl bg-white/10 px-4 py-3 text-[clamp(1rem,2.5vw,1.75rem)]">
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${isActive ? 'bg-green-400' : 'bg-gray-400'}`} />
                    <span>{p.name}</span>
                  </div>
                  {showParticipantEvents && activity && (
                    <div className="text-xs opacity-60 mt-1">
                      <div>Joined: {joinedAgo}m ago</div>
                      {!isActive && <div>Last seen: {lastSeenAgo}m ago</div>}
                    </div>
                  )}
                </li>
              )
            })}
          </ul>
        </div>
      )}

      {/* Statistics Section */}
      <div className="rounded-2xl bg-white/10 p-4">
        <button
          onClick={() => setShowStats(!showStats)}
          className="flex items-center justify-between w-full mb-3 text-[clamp(1rem,3vw,2rem)] font-semibold hover:opacity-80"
        >
          <span>Room Statistics</span>
          <span className="text-sm">{showStats ? '▼' : '▶'}</span>
        </button>

        {showStats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="bg-white/5 rounded-lg p-3">
              <div className="text-2xl font-bold text-blue-400">{stats.totalMessages}</div>
              <div className="text-sm opacity-70">Messages</div>
            </div>
            <div className="bg-white/5 rounded-lg p-3">
              <div className="text-2xl font-bold text-green-400">{stats.totalParticipants}</div>
              <div className="text-sm opacity-70">Participants</div>
            </div>
            <div className="bg-white/5 rounded-lg p-3">
              <div className="text-2xl font-bold text-yellow-400">{stats.activeTime}m</div>
              <div className="text-sm opacity-70">Active Time</div>
            </div>
            <div className="bg-white/5 rounded-lg p-3">
              <div className="text-2xl font-bold text-purple-400">{stats.messagesPerMinute}</div>
              <div className="text-sm opacity-70">Msg/Min</div>
            </div>

            {Object.keys(stats.messagesByParticipant).length > 0 && (
              <div className="col-span-2 md:col-span-4 bg-white/5 rounded-lg p-3">
                <div className="text-sm font-semibold mb-2">Messages by Participant:</div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {Object.entries(stats.messagesByParticipant)
                    .sort(([,a], [,b]) => b - a)
                    .map(([name, count]) => (
                      <div key={name} className="flex justify-between text-sm">
                        <span className="truncate">{name}:</span>
                        <span className="font-mono">{count}</span>
                      </div>
                    ))}
                </div>
              </div>
            )}

            <div className="col-span-2 md:col-span-4 bg-white/5 rounded-lg p-3">
              <div className="text-sm">
                <span className="font-semibold">Average message length:</span> {stats.averageMessageLength} characters
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="rounded-2xl bg-white/10 p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-[clamp(1rem,3vw,2rem)] font-semibold">Room Settings</h3>
            <button onClick={() => setShowSettings(false)} className="text-sm opacity-70 hover:opacity-100">✕</button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-semibold text-sm">Display Options</h4>

              <label className="flex items-center justify-between">
                <span className="text-sm">Sound Notifications</span>
                <input
                  type="checkbox"
                  checked={settings.soundEnabled}
                  onChange={(e) => {
                    setSettings(prev => ({ ...prev, soundEnabled: e.target.checked }))
                    setSoundEnabled(e.target.checked)
                  }}
                  className="rounded"
                />
              </label>

              <label className="flex items-center justify-between">
                <span className="text-sm">Auto Scroll</span>
                <input
                  type="checkbox"
                  checked={settings.autoScroll}
                  onChange={(e) => {
                    setSettings(prev => ({ ...prev, autoScroll: e.target.checked }))
                    setAutoScroll(e.target.checked)
                  }}
                  className="rounded"
                />
              </label>

              <label className="flex items-center justify-between">
                <span className="text-sm">Show Timestamps</span>
                <input
                  type="checkbox"
                  checked={settings.showTimestamps}
                  onChange={(e) => setSettings(prev => ({ ...prev, showTimestamps: e.target.checked }))}
                  className="rounded"
                />
              </label>

              <label className="flex items-center justify-between">
                <span className="text-sm">Compact Mode</span>
                <input
                  type="checkbox"
                  checked={settings.compactMode}
                  onChange={(e) => setSettings(prev => ({ ...prev, compactMode: e.target.checked }))}
                  className="rounded"
                />
              </label>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-sm">Performance</h4>

              <label className="block">
                <span className="text-sm">Max Messages ({settings.maxMessages})</span>
                <input
                  type="range"
                  min="50"
                  max="500"
                  step="50"
                  value={settings.maxMessages}
                  onChange={(e) => setSettings(prev => ({ ...prev, maxMessages: parseInt(e.target.value) }))}
                  className="w-full mt-1"
                />
                <div className="flex justify-between text-xs opacity-60 mt-1">
                  <span>50</span>
                  <span>500</span>
                </div>
              </label>

              <div className="bg-white/5 rounded-lg p-3">
                <div className="text-sm font-semibold mb-2">Current Stats</div>
                <div className="text-xs space-y-1 opacity-80">
                  <div>Messages: {messages.length}</div>
                  <div>Participants: {visible.length}</div>
                  <div>Memory Usage: ~{Math.round(JSON.stringify(messages).length / 1024)}KB</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="rounded-2xl bg-white/10 p-4">
        <div className="mb-4 space-y-3">
          <div className="flex items-center justify-between">
            <div className="text-[clamp(1rem,3vw,2rem)] font-semibold">Live Messages</div>
          </div>
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Search messages..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1 px-3 py-2 text-sm bg-white/10 rounded-lg border border-white/20 focus:border-white/40 focus:outline-none"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="px-3 py-2 text-sm bg-white/10 hover:bg-white/20 rounded-lg"
              >
                Clear
              </button>
            )}
          </div>
          {allParticipantNames.length > 0 && (
            <div className="flex flex-wrap gap-2">
              <span className="text-sm opacity-70">Filter by:</span>
              {allParticipantNames.map(name => (
                <button
                  key={name}
                  onClick={() => toggleParticipantFilter(name)}
                  className={`px-2 py-1 text-xs rounded-lg transition-colors ${
                    selectedParticipants.has(name)
                      ? 'bg-blue-500/30 text-blue-200'
                      : 'bg-white/10 hover:bg-white/20'
                  }`}
                >
                  {name} {selectedParticipants.has(name) ? '✓' : ''}
                </button>
              ))}
              {selectedParticipants.size > 0 && (
                <button
                  onClick={() => setSelectedParticipants(new Set())}
                  className="px-2 py-1 text-xs rounded-lg bg-red-500/20 hover:bg-red-500/30"
                >
                  Clear filters
                </button>
              )}
            </div>
          )}
          <div className="flex gap-2">
            <button onClick={() => document.documentElement.requestFullscreen?.()} className="rounded-lg bg-white/20 px-3 py-1 text-sm">Fullscreen (f)</button>
            <button onClick={clearMessages} className="rounded-lg bg-white/20 px-3 py-1 text-sm">Clear (c)</button>
            <button onClick={() => setHideNames((v) => !v)} className="rounded-lg bg-white/20 px-3 py-1 text-sm">
              {hideNames ? 'Show Names' : 'Hide Names'} (h)
            </button>
            <button onClick={() => setSoundEnabled((v) => !v)} className={`rounded-lg px-3 py-1 text-sm ${soundEnabled ? 'bg-green-500/20' : 'bg-white/20'}`}>
              {soundEnabled ? '🔊' : '🔇'}
            </button>
            <button onClick={() => setAutoScroll((v) => !v)} className={`rounded-lg px-3 py-1 text-sm ${autoScroll ? 'bg-blue-500/20' : 'bg-white/20'}`}>
              {autoScroll ? '📜' : '⏸️'}
            </button>
            <div className="relative group">
              <button className="rounded-lg bg-white/20 px-3 py-1 text-sm">Export 📥</button>
              <div className="absolute right-0 top-full mt-1 hidden group-hover:block bg-black/80 rounded-lg p-2 space-y-1 min-w-[100px]">
                <button onClick={() => exportMessages('txt')} className="block w-full text-left px-2 py-1 text-sm hover:bg-white/20 rounded">Text</button>
                <button onClick={() => exportMessages('json')} className="block w-full text-left px-2 py-1 text-sm hover:bg-white/20 rounded">JSON</button>
              </div>
            </div>
            <button onClick={() => setShowSettings(!showSettings)} className="rounded-lg bg-white/20 px-3 py-1 text-sm">
              Settings ⚙️
            </button>
          </div>
        </div>
        <div ref={scrollRef} className="flex max-h-[60vh] flex-col gap-6 overflow-y-auto pr-2" onScroll={handleScroll}>
          {filteredMessages.length > 50 ? (
            // Virtual scrolling for large lists
            <div style={{ height: filteredMessages.length * ITEM_HEIGHT, position: 'relative' }}>
              {visibleMessages.map((m: any) => (
                <div
                  key={m.id}
                  className="text-[clamp(1rem,3.5vw,2.5rem)] absolute w-full"
                  style={{
                    top: (m.virtualIndex || 0) * ITEM_HEIGHT,
                    height: ITEM_HEIGHT
                  }}
                >
                  <Message
                    msg={m}
                    isNew={newMessageIds.has(m.id)}
                    searchQuery={searchQuery}
                    onReaction={handleReaction}
                  />
                </div>
              ))}
            </div>
          ) : (
            // Regular rendering for small lists
            <>
              {filteredMessages.map((m) => (
                <div key={m.id} className="text-[clamp(1rem,3.5vw,2.5rem)]">
                  <Message
                    msg={m}
                    isNew={newMessageIds.has(m.id)}
                    searchQuery={searchQuery}
                    onReaction={handleReaction}
                  />
                </div>
              ))}
              {filteredMessages.length === 0 && messages.length > 0 && (
                <div className="text-center opacity-50 py-8">
                  No messages from selected participants
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
}
