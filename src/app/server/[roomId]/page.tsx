
'use client'
import { useEffect, useMemo, useRef, useState } from 'react'
import io, { Socket } from 'socket.io-client'
import QR from '@/components/QR'
import Message from '@/components/Message'
import type { Message as M, Participant } from '@/lib/types'

export default function ServerView({ params }: { params: { roomId: string } }) {
  const { roomId } = params
  const [participants, setParticipants] = useState<Participant[]>([])
  const [messages, setMessages] = useState<M[]>([])
  const [hideNames, setHideNames] = useState(false)
  const sockRef = useRef<Socket | null>(null)
  const scrollRef = useRef<HTMLDivElement>(null)

  // init socket inside component to avoid stale global instance
  useEffect(() => {
    const s = io(window.location.origin, { transports: ['websocket'] })
    sockRef.current = s

    const join = () => s.emit('room:join', { roomId, name: 'Server' })
    s.on('connect', join)
    join() // first load

    s.on('room:state', (state: { participants: Participant[]; messages: M[] }) => {
      setParticipants(state.participants)
      setMessages(state.messages)
    })
    s.on('room:message', (msg: M) => setMessages((prev) => [...prev, msg]))

    return () => {
      s.off('connect', join)
      s.off('room:state')
      s.off('room:message')
      s.disconnect()
    }
  }, [roomId])

  // auto-scroll on new messages
  useEffect(() => {
    const el = scrollRef.current
    if (el) el.scrollTop = el.scrollHeight
  }, [messages])

  const joinUrl = useMemo(() => {
    const base = process.env.NEXT_PUBLIC_BASE_URL || window.location.origin
    return `${base}/room/${roomId}`
  }, [roomId])

  const clearMessages = () => sockRef.current?.emit('room:clear', { roomId })
  const visible = participants.filter((p) => (p?.name ?? '').trim() && p.name !== 'Server')

  return (
    <div className="space-y-8 select-none">
      <div className="flex items-center justify-between gap-6">
        <div>
          <div className="text-sm uppercase tracking-[0.3em] opacity-80">Room</div>
          <div className="font-black leading-none text-[clamp(2rem,10vw,8rem)]">{roomId}</div>
          <div className="opacity-80">Scan to join</div>
        </div>
        <div className="flex flex-col items-center gap-2">
          <QR text={joinUrl} size={260} />
          <div className="text-xs opacity-80 max-w-[260px] text-center break-all">{joinUrl}</div>
        </div>
      </div>

      {!hideNames && (
        <div>
          <div className="mb-3 text-[clamp(1rem,3vw,2rem)] font-semibold">
            Participants ({visible.length})
          </div>
          <ul className="grid gap-3 [grid-template-columns:repeat(auto-fit,minmax(12ch,1fr))]">
            {visible.map((p) => (
              <li key={p.id} className="rounded-xl bg-white/10 px-4 py-3 text-[clamp(1rem,2.5vw,1.75rem)]">
                {p.name}
              </li>
            ))}
          </ul>
        </div>
      )}

      <div className="rounded-2xl bg-white/10 p-4">
        <div className="mb-2 flex items-center justify-between">
          <div className="text-[clamp(1rem,3vw,2rem)] font-semibold">Live Messages</div>
          <div className="flex gap-2">
            <button onClick={() => document.documentElement.requestFullscreen?.()} className="rounded-lg bg-white/20 px-3 py-1 text-sm">Fullscreen (f)</button>
            <button onClick={clearMessages} className="rounded-lg bg-white/20 px-3 py-1 text-sm">Clear (c)</button>
            <button onClick={() => setHideNames((v) => !v)} className="rounded-lg bg-white/20 px-3 py-1 text-sm">
              {hideNames ? 'Show Names' : 'Hide Names'} (h)
            </button>
          </div>
        </div>
        <div ref={scrollRef} className="flex max-h-[60vh] flex-col gap-6 overflow-y-auto pr-2">
          {messages.map((m) => (
            <div key={m.id} className="text-[clamp(1rem,3.5vw,2.5rem)]">
              <Message msg={m} />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
