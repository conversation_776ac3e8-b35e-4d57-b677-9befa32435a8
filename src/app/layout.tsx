import './globals.css'
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Realtime QR Room',
  description: 'Server & participant views with Socket.IO'
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className="min-h-screen bg-gradient-to-br from-brand-800 via-brand-600 to-brand-500 text-white">
        <div className="mx-auto max-w-6xl p-6">{children}</div>
      </body>
    </html>
  )
}