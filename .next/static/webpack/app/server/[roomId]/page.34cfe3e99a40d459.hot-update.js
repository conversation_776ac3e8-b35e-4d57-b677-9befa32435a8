"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/server/[roomId]/page",{

/***/ "(app-pages-browser)/./src/app/server/[roomId]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/server/[roomId]/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ServerView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var _components_QR__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/QR */ \"(app-pages-browser)/./src/components/QR.tsx\");\n/* harmony import */ var _components_Message__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Message */ \"(app-pages-browser)/./src/components/Message.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/../../../../node_modules/process/browser.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ServerView(param) {\n    let { params } = param;\n    _s();\n    const { roomId } = params;\n    const [participants, setParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [hideNames, setHideNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sockRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // init socket inside component to avoid stale global instance\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const s = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(window.location.origin, {\n            transports: [\n                \"websocket\"\n            ]\n        });\n        sockRef.current = s;\n        const join = ()=>s.emit(\"room:join\", {\n                roomId,\n                name: \"Server\"\n            });\n        s.on(\"connect\", join);\n        join() // first load\n        ;\n        s.on(\"room:state\", (state)=>{\n            setParticipants(state.participants);\n            setMessages(state.messages);\n        });\n        s.on(\"room:message\", (msg)=>setMessages((prev)=>[\n                    ...prev,\n                    msg\n                ]));\n        return ()=>{\n            s.off(\"connect\", join);\n            s.off(\"room:state\");\n            s.off(\"room:message\");\n            s.disconnect();\n        };\n    }, [\n        roomId\n    ]);\n    // auto-scroll on new messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const el = scrollRef.current;\n        if (el) el.scrollTop = el.scrollHeight;\n    }, [\n        messages\n    ]);\n    const joinUrl = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const base = process.env.NEXT_PUBLIC_BASE_URL || window.location.origin;\n        return \"\".concat(base, \"/room/\").concat(roomId);\n    }, [\n        roomId\n    ]);\n    const clearMessages = ()=>{\n        var _sockRef_current;\n        return (_sockRef_current = sockRef.current) === null || _sockRef_current === void 0 ? void 0 : _sockRef_current.emit(\"room:clear\", {\n            roomId\n        });\n    };\n    const visible = participants.filter((p)=>{\n        var _p_name;\n        return ((_p_name = p === null || p === void 0 ? void 0 : p.name) !== null && _p_name !== void 0 ? _p_name : \"\").trim() && p.name !== \"Server\";\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 select-none\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm uppercase tracking-[0.3em] opacity-80\",\n                                children: \"Room\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-black leading-none text-[clamp(2rem,10vw,8rem)]\",\n                                children: roomId\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"opacity-80\",\n                                children: \"Scan to join\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QR__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                text: joinUrl,\n                                size: 260\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs opacity-80 max-w-[260px] text-center break-all\",\n                                children: joinUrl\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            !hideNames && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3 text-[clamp(1rem,3vw,2rem)] font-semibold\",\n                        children: [\n                            \"Participants (\",\n                            visible.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"grid gap-3 [grid-template-columns:repeat(auto-fit,minmax(12ch,1fr))]\",\n                        children: visible.map((p)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"rounded-xl bg-white/10 px-4 py-3 text-[clamp(1rem,2.5vw,1.75rem)]\",\n                                children: p.name\n                            }, p.id, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-2xl bg-white/10 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-[clamp(1rem,3vw,2rem)] font-semibold\",\n                                children: \"Live Messages\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            var _document_documentElement_requestFullscreen, _document_documentElement;\n                                            return (_document_documentElement_requestFullscreen = (_document_documentElement = document.documentElement).requestFullscreen) === null || _document_documentElement_requestFullscreen === void 0 ? void 0 : _document_documentElement_requestFullscreen.call(_document_documentElement);\n                                        },\n                                        className: \"rounded-lg bg-white/20 px-3 py-1 text-sm\",\n                                        children: \"Fullscreen (f)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: clearMessages,\n                                        className: \"rounded-lg bg-white/20 px-3 py-1 text-sm\",\n                                        children: \"Clear (c)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setHideNames((v)=>!v),\n                                        className: \"rounded-lg bg-white/20 px-3 py-1 text-sm\",\n                                        children: [\n                                            hideNames ? \"Show Names\" : \"Hide Names\",\n                                            \" (h)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: scrollRef,\n                        className: \"flex max-h-[60vh] flex-col gap-6 overflow-y-auto pr-2\",\n                        children: messages.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-[clamp(1rem,3.5vw,2.5rem)]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Message__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    msg: m\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            }, m.id, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(ServerView, \"xe3UtwobzN7DKQiYSInaKhBr63g=\");\n_c = ServerView;\nvar _c;\n$RefreshReg$(_c, \"ServerView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/server/[roomId]/page.tsx\n"));

/***/ })

});