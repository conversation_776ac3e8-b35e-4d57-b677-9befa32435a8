"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/server/[roomId]/page",{

/***/ "(app-pages-browser)/./src/app/server/[roomId]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/server/[roomId]/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ServerView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var _components_QR__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/QR */ \"(app-pages-browser)/./src/components/QR.tsx\");\n/* harmony import */ var _components_Message__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Message */ \"(app-pages-browser)/./src/components/Message.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/../../../../node_modules/process/browser.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ServerView(param) {\n    let { params } = param;\n    _s();\n    const { roomId } = params;\n    const [participants, setParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [hideNames, setHideNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sockRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // init socket inside component to avoid stale global instance\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const s = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(window.location.origin, {\n            transports: [\n                \"websocket\"\n            ]\n        });\n        sockRef.current = s;\n        const join = ()=>s.emit(\"room:join\", {\n                roomId,\n                name: \"Server\"\n            });\n        s.on(\"connect\", join);\n        join() // first load\n        ;\n        s.on(\"room:state\", (state)=>{\n            setParticipants(state.participants);\n            setMessages(state.messages);\n        });\n        s.on(\"room:message\", (msg)=>setMessages((prev)=>[\n                    ...prev,\n                    msg\n                ]));\n        return ()=>{\n            s.off(\"connect\", join);\n            s.off(\"room:state\");\n            s.off(\"room:message\");\n            s.disconnect();\n        };\n    }, [\n        roomId\n    ]);\n    // auto-scroll on new messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const el = scrollRef.current;\n        if (el) el.scrollTop = el.scrollHeight;\n    }, [\n        messages\n    ]);\n    const joinUrl = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const base = process.env.NEXT_PUBLIC_BASE_URL || window.location.origin;\n        return \"\".concat(base, \"/room/\").concat(roomId);\n    }, [\n        roomId\n    ]);\n    const clearMessages = ()=>{\n        var _sockRef_current;\n        return (_sockRef_current = sockRef.current) === null || _sockRef_current === void 0 ? void 0 : _sockRef_current.emit(\"room:clear\", {\n            roomId\n        });\n    };\n    const visible = participants.filter((p)=>{\n        var _p_name;\n        return ((_p_name = p === null || p === void 0 ? void 0 : p.name) !== null && _p_name !== void 0 ? _p_name : \"\").trim() && p.name !== \"Server\";\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 select-none\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm uppercase tracking-[0.3em] opacity-80\",\n                                children: \"Room\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-black leading-none text-[clamp(2rem,10vw,8rem)]\",\n                                children: roomId\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"opacity-80\",\n                                children: \"Scan to join\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QR__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                text: joinUrl,\n                                size: 260\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs opacity-80 max-w-[260px] text-center break-all\",\n                                children: joinUrl\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            !hideNames && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3 text-[clamp(1rem,3vw,2rem)] font-semibold\",\n                        children: [\n                            \"Participants (\",\n                            visible.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"grid gap-3 [grid-template-columns:repeat(auto-fit,minmax(12ch,1fr))]\",\n                        children: visible.map((p)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"rounded-xl bg-white/10 px-4 py-3 text-[clamp(1rem,2.5vw,1.75rem)]\",\n                                children: p.name\n                            }, p.id, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-2xl bg-white/10 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-[clamp(1rem,3vw,2rem)] font-semibold\",\n                                children: \"Live Messages\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            var _document_documentElement_requestFullscreen, _document_documentElement;\n                                            return (_document_documentElement_requestFullscreen = (_document_documentElement = document.documentElement).requestFullscreen) === null || _document_documentElement_requestFullscreen === void 0 ? void 0 : _document_documentElement_requestFullscreen.call(_document_documentElement);\n                                        },\n                                        className: \"rounded-lg bg-white/20 px-3 py-1 text-sm\",\n                                        children: \"Fullscreen (f)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: clearMessages,\n                                        className: \"rounded-lg bg-white/20 px-3 py-1 text-sm\",\n                                        children: \"Clear (c)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setHideNames((v)=>!v),\n                                        className: \"rounded-lg bg-white/20 px-3 py-1 text-sm\",\n                                        children: [\n                                            hideNames ? \"Show Names\" : \"Hide Names\",\n                                            \" (h)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: scrollRef,\n                        className: \"flex max-h-[60vh] flex-col gap-4 overflow-y-auto pr-2\",\n                        children: messages.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-[clamp(1rem,3.5vw,2.5rem)]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Message__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    msg: m\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)\n                            }, m.id, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_s(ServerView, \"xe3UtwobzN7DKQiYSInaKhBr63g=\");\n_c = ServerView;\nvar _c;\n$RefreshReg$(_c, \"ServerView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/server/[roomId]/page.tsx\n"));

/***/ })

});