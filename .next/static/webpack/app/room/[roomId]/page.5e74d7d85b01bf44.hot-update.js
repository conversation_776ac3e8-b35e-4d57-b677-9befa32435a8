"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/room/[roomId]/page",{

/***/ "(app-pages-browser)/./src/app/room/[roomId]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/room/[roomId]/page.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RoomView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction RoomView(param) {\n    let { params } = param;\n    _s();\n    const { roomId } = params;\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [text, setText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [joined, setJoined] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sockRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const s = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(window.location.origin, {\n            transports: [\n                \"websocket\"\n            ]\n        });\n        sockRef.current = s;\n        const tryJoin = ()=>{\n            if (joined && name.trim()) s.emit(\"room:join\", {\n                roomId,\n                name: name.trim()\n            });\n        };\n        s.on(\"connect\", tryJoin);\n        tryJoin();\n        return ()=>{\n            s.off(\"connect\", tryJoin);\n            s.disconnect();\n        };\n    }, [\n        joined,\n        name,\n        roomId\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mx-auto max-w-md space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: [\n                    \"Room \",\n                    roomId\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            !joined ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"space-y-3 rounded-2xl bg-white/10 p-4\",\n                onSubmit: (e)=>{\n                    e.preventDefault();\n                    if (!name.trim()) return;\n                    setJoined(true);\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm opacity-80\",\n                        children: \"Display name\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        className: \"w-full rounded-lg bg-white/10 px-3 py-2 outline-none ring-1 ring-white/20\",\n                        value: name,\n                        onChange: (e)=>setName(e.target.value),\n                        placeholder: \"Your name\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full rounded-xl bg-brand-500 px-4 py-2 font-semibold hover:bg-brand-600\",\n                        children: \"Join\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"space-y-3 rounded-2xl bg-white/10 p-4\",\n                onSubmit: (e)=>{\n                    var _sockRef_current;\n                    e.preventDefault();\n                    if (!text.trim()) return;\n                    (_sockRef_current = sockRef.current) === null || _sockRef_current === void 0 ? void 0 : _sockRef_current.emit(\"room:message\", {\n                        roomId,\n                        text,\n                        name: name.trim()\n                    });\n                    setText(\"\");\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm opacity-80\",\n                        children: \"Post a message\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        className: \"w-full rounded-lg bg-white/10 px-3 py-2 outline-none ring-1 ring-white/20\",\n                        value: text,\n                        onChange: (e)=>setText(e.target.value),\n                        placeholder: \"Say something…\",\n                        maxLength: 140\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full rounded-xl bg-white/20 px-4 py-2\",\n                        children: \"Send\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this),\n            joined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Toast__WEBPACK_IMPORTED_MODULE_3__.Toast, {\n                text: \"Joined as \".concat(name.trim())\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                lineNumber: 72,\n                columnNumber: 18\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_s(RoomView, \"RqtRw7Dj4kZ+Pj+3ngImGO6IdhQ=\");\n_c = RoomView;\nvar _c;\n$RefreshReg$(_c, \"RoomView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/room/[roomId]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Toast.tsx":
/*!**********************************!*\
  !*** ./src/components/Toast.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: function() { return /* binding */ Toast; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Toast auto */ \nvar _s = $RefreshSig$();\n\nfunction Toast(param) {\n    let { text } = param;\n    _s();\n    const [show, setShow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const t = setTimeout(()=>setShow(false), 2500);\n        return ()=>clearTimeout(t);\n    }, []);\n    if (!show) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 left-1/2 -translate-x-1/2 rounded-xl bg-black/70 px-4 py-2 text-sm shadow-lg\",\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Toast.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_s(Toast, \"7rrxj0wE5iz58YydTDN/pg4Izes=\");\n_c = Toast;\nvar _c;\n$RefreshReg$(_c, \"Toast\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1RvYXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDMkM7QUFFcEMsU0FBU0UsTUFBTSxLQUEwQjtRQUExQixFQUFFQyxJQUFJLEVBQW9CLEdBQTFCOztJQUNwQixNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR0osK0NBQVFBLENBQUM7SUFDakNELGdEQUFTQSxDQUFDO1FBQ1IsTUFBTU0sSUFBSUMsV0FBVyxJQUFNRixRQUFRLFFBQVE7UUFDM0MsT0FBTyxJQUFNRyxhQUFhRjtJQUM1QixHQUFHLEVBQUU7SUFDTCxJQUFJLENBQUNGLE1BQU0sT0FBTztJQUNsQixxQkFDRSw4REFBQ0s7UUFBSUMsV0FBVTtrQkFDWlA7Ozs7OztBQUdQO0dBWmdCRDtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9Ub2FzdC50c3g/YTc1MiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcblxuZXhwb3J0IGZ1bmN0aW9uIFRvYXN0KHsgdGV4dCB9OiB7IHRleHQ6IHN0cmluZyB9KSB7XG4gIGNvbnN0IFtzaG93LCBzZXRTaG93XSA9IHVzZVN0YXRlKHRydWUpXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdCA9IHNldFRpbWVvdXQoKCkgPT4gc2V0U2hvdyhmYWxzZSksIDI1MDApXG4gICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0KVxuICB9LCBbXSlcbiAgaWYgKCFzaG93KSByZXR1cm4gbnVsbFxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgYm90dG9tLTYgbGVmdC0xLzIgLXRyYW5zbGF0ZS14LTEvMiByb3VuZGVkLXhsIGJnLWJsYWNrLzcwIHB4LTQgcHktMiB0ZXh0LXNtIHNoYWRvdy1sZ1wiPlxuICAgICAge3RleHR9XG4gICAgPC9kaXY+XG4gIClcbn0iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJUb2FzdCIsInRleHQiLCJzaG93Iiwic2V0U2hvdyIsInQiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwiZGl2IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Toast.tsx\n"));

/***/ })

});