/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/server/[roomId]/page";
exports.ids = ["app/server/[roomId]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?d272":
/*!********************************!*\
  !*** supports-color (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fserver%2F%5BroomId%5D%2Fpage&page=%2Fserver%2F%5BroomId%5D%2Fpage&appPaths=%2Fserver%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Fserver%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fserver%2F%5BroomId%5D%2Fpage&page=%2Fserver%2F%5BroomId%5D%2Fpage&appPaths=%2Fserver%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Fserver%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'server',\n        {\n        children: [\n        '[roomId]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/server/[roomId]/page.tsx */ \"(rsc)/./src/app/server/[roomId]/page.tsx\")), \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/server/[roomId]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/server/[roomId]/page\",\n        pathname: \"/server/[roomId]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZzZXJ2ZXIlMkYlNUJyb29tSWQlNUQlMkZwYWdlJnBhZ2U9JTJGc2VydmVyJTJGJTVCcm9vbUlkJTVEJTJGcGFnZSZhcHBQYXRocz0lMkZzZXJ2ZXIlMkYlNUJyb29tSWQlNUQlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGc2VydmVyJTJGJTVCcm9vbUlkJTVEJTJGcGFnZS50c3gmYXBwRGlyPSUyRlVzZXJzJTJGbnVsbCUyRkRvY3VtZW50cyUyRkRldmVsb3BtZW50JTJGU2VwdC0yMDI1JTJGcmVhbHRpbWUtcm9vbSUyRnNyYyUyRmFwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9JTJGVXNlcnMlMkZudWxsJTJGRG9jdW1lbnRzJTJGRGV2ZWxvcG1lbnQlMkZTZXB0LTIwMjUlMkZyZWFsdGltZS1yb29tJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsYUFBYSxzQkFBc0I7QUFDaUU7QUFDckM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLHVCQUF1QixnTEFBK0g7QUFDdEo7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLHlCQUF5QixvSkFBaUg7QUFDMUksb0JBQW9CLDBOQUFnRjtBQUNwRztBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFDNkQ7QUFDcEYsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDdUQ7QUFDdkQ7QUFDTyx3QkFBd0IsOEdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWx0aW1lLXFyLXJvb20vPzljMzAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnc2VydmVyJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnW3Jvb21JZF0nLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbnVsbC9Eb2N1bWVudHMvRGV2ZWxvcG1lbnQvU2VwdC0yMDI1L3JlYWx0aW1lLXJvb20vc3JjL2FwcC9zZXJ2ZXIvW3Jvb21JZF0vcGFnZS50c3hcIiksIFwiL1VzZXJzL251bGwvRG9jdW1lbnRzL0RldmVsb3BtZW50L1NlcHQtMjAyNS9yZWFsdGltZS1yb29tL3NyYy9hcHAvc2VydmVyL1tyb29tSWRdL3BhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9udWxsL0RvY3VtZW50cy9EZXZlbG9wbWVudC9TZXB0LTIwMjUvcmVhbHRpbWUtcm9vbS9zcmMvYXBwL2xheW91dC50c3hcIiksIFwiL1VzZXJzL251bGwvRG9jdW1lbnRzL0RldmVsb3BtZW50L1NlcHQtMjAyNS9yZWFsdGltZS1yb29tL3NyYy9hcHAvbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiL1VzZXJzL251bGwvRG9jdW1lbnRzL0RldmVsb3BtZW50L1NlcHQtMjAyNS9yZWFsdGltZS1yb29tL3NyYy9hcHAvc2VydmVyL1tyb29tSWRdL3BhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvc2VydmVyL1tyb29tSWRdL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvc2VydmVyL1tyb29tSWRdL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL3NlcnZlci9bcm9vbUlkXVwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fserver%2F%5BroomId%5D%2Fpage&page=%2Fserver%2F%5BroomId%5D%2Fpage&appPaths=%2Fserver%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Fserver%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Fserver%2F%5BroomId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Fserver%2F%5BroomId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/server/[roomId]/page.tsx */ \"(ssr)/./src/app/server/[roomId]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbnVsbCUyRkRvY3VtZW50cyUyRkRldmVsb3BtZW50JTJGU2VwdC0yMDI1JTJGcmVhbHRpbWUtcm9vbSUyRnNyYyUyRmFwcCUyRnNlcnZlciUyRiU1QnJvb21JZCU1RCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBK0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFsdGltZS1xci1yb29tLz8zYzQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL251bGwvRG9jdW1lbnRzL0RldmVsb3BtZW50L1NlcHQtMjAyNS9yZWFsdGltZS1yb29tL3NyYy9hcHAvc2VydmVyL1tyb29tSWRdL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Fserver%2F%5BroomId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/server/[roomId]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/server/[roomId]/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServerView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _components_QR__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/QR */ \"(ssr)/./src/components/QR.tsx\");\n/* harmony import */ var _components_Message__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Message */ \"(ssr)/./src/components/Message.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ServerView({ params }) {\n    const { roomId } = params;\n    const [participants, setParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [hideNames, setHideNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"connecting\");\n    const [soundEnabled, setSoundEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [autoScroll, setAutoScroll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedParticipants, setSelectedParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showStats, setShowStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newMessageIds, setNewMessageIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [roomStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const [participantActivity, setParticipantActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showParticipantEvents, setShowParticipantEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [reconnectAttempts, setReconnectAttempts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        maxMessages: 100,\n        soundEnabled: true,\n        autoScroll: true,\n        showTimestamps: true,\n        compactMode: false\n    });\n    const sockRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Virtual scrolling state\n    const [scrollTop, setScrollTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [containerHeight, setContainerHeight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const ITEM_HEIGHT = 120 // Approximate height of each message\n    ;\n    const BUFFER_SIZE = 5 // Number of items to render outside visible area\n    ;\n    const MAX_MESSAGES = 100 // Limit messages to prevent memory issues\n    ;\n    // Initialize audio for notifications\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Create a simple notification sound using Web Audio API\n        const createNotificationSound = ()=>{\n            const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);\n            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);\n            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);\n            oscillator.start(audioContext.currentTime);\n            oscillator.stop(audioContext.currentTime + 0.2);\n        };\n        audioRef.current = {\n            play: createNotificationSound\n        };\n    }, []);\n    const playNotificationSound = ()=>{\n        if (soundEnabled && audioRef.current) {\n            try {\n                audioRef.current.play();\n            } catch (error) {\n                console.log(\"Could not play notification sound:\", error);\n            }\n        }\n    };\n    // init socket inside component to avoid stale global instance\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const s = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(window.location.origin, {\n            transports: [\n                \"websocket\"\n            ],\n            reconnection: true,\n            reconnectionAttempts: 5,\n            reconnectionDelay: 1000,\n            timeout: 10000\n        });\n        sockRef.current = s;\n        const join = ()=>{\n            try {\n                s.emit(\"room:join\", {\n                    roomId,\n                    name: \"Server\"\n                });\n                setError(null);\n            } catch (err) {\n                setError(\"Failed to join room\");\n                console.error(\"Join error:\", err);\n            }\n        };\n        s.on(\"connect\", ()=>{\n            setConnectionStatus(\"connected\");\n            setReconnectAttempts(0);\n            setError(null);\n            join();\n        });\n        s.on(\"disconnect\", (reason)=>{\n            setConnectionStatus(\"disconnected\");\n            if (reason === \"io server disconnect\") {\n                setError(\"Server disconnected the connection\");\n            } else {\n                setError(\"Connection lost - attempting to reconnect...\");\n            }\n        });\n        s.on(\"connect_error\", (err)=>{\n            setConnectionStatus(\"disconnected\");\n            setReconnectAttempts((prev)=>prev + 1);\n            setError(`Connection failed: ${err.message || \"Unknown error\"}`);\n            console.error(\"Connection error:\", err);\n        });\n        s.on(\"reconnect\", (attemptNumber)=>{\n            setConnectionStatus(\"connected\");\n            setReconnectAttempts(0);\n            setError(null);\n            console.log(`Reconnected after ${attemptNumber} attempts`);\n        });\n        s.on(\"reconnect_error\", (err)=>{\n            setError(`Reconnection failed: ${err.message || \"Unknown error\"}`);\n            console.error(\"Reconnection error:\", err);\n        });\n        s.on(\"reconnect_failed\", ()=>{\n            setError(\"Failed to reconnect after maximum attempts\");\n            setConnectionStatus(\"disconnected\");\n        });\n        join() // first load\n        ;\n        s.on(\"room:state\", (state)=>{\n            try {\n                if (!state || !Array.isArray(state.participants) || !Array.isArray(state.messages)) {\n                    throw new Error(\"Invalid room state received\");\n                }\n                setParticipants(state.participants);\n                setMessages(state.messages.slice(-MAX_MESSAGES)) // Keep only latest messages\n                ;\n                // Track participant activity\n                const now = Date.now();\n                setParticipantActivity((prev)=>{\n                    const updated = {\n                        ...prev\n                    };\n                    state.participants.forEach((p)=>{\n                        if (!p || !p.name) return; // Skip invalid participants\n                        if (!updated[p.name]) {\n                            updated[p.name] = {\n                                joinTime: now,\n                                lastSeen: now\n                            };\n                        } else {\n                            updated[p.name].lastSeen = now;\n                        }\n                    });\n                    return updated;\n                });\n                setError(null) // Clear any previous errors\n                ;\n            } catch (err) {\n                setError(`Failed to process room state: ${err instanceof Error ? err.message : \"Unknown error\"}`);\n                console.error(\"Room state error:\", err);\n            }\n        });\n        s.on(\"room:reaction\", (data)=>{\n            try {\n                if (!data || !data.messageId || !data.emoji || !data.name) {\n                    throw new Error(\"Invalid reaction data received\");\n                }\n                setMessages((prev)=>prev.map((msg)=>{\n                        if (msg.id === data.messageId) {\n                            const reactions = {\n                                ...msg.reactions\n                            };\n                            if (!reactions[data.emoji]) {\n                                reactions[data.emoji] = [];\n                            }\n                            // Toggle reaction - remove if already exists, add if not\n                            const userIndex = reactions[data.emoji].indexOf(data.name);\n                            if (userIndex > -1) {\n                                reactions[data.emoji].splice(userIndex, 1);\n                                if (reactions[data.emoji].length === 0) {\n                                    delete reactions[data.emoji];\n                                }\n                            } else {\n                                reactions[data.emoji].push(data.name);\n                            }\n                            return {\n                                ...msg,\n                                reactions\n                            };\n                        }\n                        return msg;\n                    }));\n            } catch (err) {\n                setError(`Failed to process reaction: ${err instanceof Error ? err.message : \"Unknown error\"}`);\n                console.error(\"Reaction error:\", err);\n            }\n        });\n        s.on(\"room:message\", (msg)=>{\n            try {\n                if (!msg || !msg.id || !msg.name || typeof msg.text !== \"string\" || !msg.ts) {\n                    throw new Error(\"Invalid message data received\");\n                }\n                setMessages((prev)=>{\n                    const newMessages = [\n                        ...prev,\n                        msg\n                    ];\n                    return newMessages.slice(-MAX_MESSAGES) // Auto-cleanup old messages\n                    ;\n                });\n                // Update participant activity\n                setParticipantActivity((prev)=>({\n                        ...prev,\n                        [msg.name]: {\n                            ...prev[msg.name],\n                            lastSeen: Date.now(),\n                            joinTime: prev[msg.name]?.joinTime || Date.now()\n                        }\n                    }));\n                // Mark message as new for animation\n                setNewMessageIds((prev)=>new Set([\n                        ...prev,\n                        msg.id\n                    ]));\n                // Remove the new flag after animation completes\n                setTimeout(()=>{\n                    setNewMessageIds((prev)=>{\n                        const newSet = new Set(prev);\n                        newSet.delete(msg.id);\n                        return newSet;\n                    });\n                }, 500);\n                // Play notification sound for new messages (but not from Server)\n                if (msg.name !== \"Server\") {\n                    playNotificationSound();\n                }\n            } catch (err) {\n                setError(`Failed to process message: ${err instanceof Error ? err.message : \"Unknown error\"}`);\n                console.error(\"Message error:\", err);\n            }\n        });\n        return ()=>{\n            s.off(\"connect\");\n            s.off(\"disconnect\");\n            s.off(\"connect_error\");\n            s.off(\"room:state\");\n            s.off(\"room:message\");\n            s.off(\"room:reaction\");\n            s.disconnect();\n        };\n    }, [\n        roomId\n    ]);\n    // auto-scroll on new messages (only if enabled)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (autoScroll) {\n            const el = scrollRef.current;\n            if (el) el.scrollTop = el.scrollHeight;\n        }\n    }, [\n        messages,\n        autoScroll\n    ]);\n    // Detect manual scrolling to disable auto-scroll and update virtual scroll\n    const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const el = scrollRef.current;\n        if (el) {\n            const newScrollTop = el.scrollTop;\n            const newContainerHeight = el.clientHeight;\n            setScrollTop(newScrollTop);\n            setContainerHeight(newContainerHeight);\n            if (autoScroll) {\n                const isAtBottom = el.scrollHeight - newScrollTop <= newContainerHeight + 50 // 50px threshold\n                ;\n                if (!isAtBottom) {\n                    setAutoScroll(false);\n                }\n            }\n        }\n    }, [\n        autoScroll\n    ]);\n    // Update container height on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const el = scrollRef.current;\n        if (el) {\n            setContainerHeight(el.clientHeight);\n            const resizeObserver = new ResizeObserver((entries)=>{\n                for (const entry of entries){\n                    setContainerHeight(entry.contentRect.height);\n                }\n            });\n            resizeObserver.observe(el);\n            return ()=>resizeObserver.disconnect();\n        }\n    }, []);\n    const joinUrl = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const base = process.env.NEXT_PUBLIC_BASE_URL || window.location.origin;\n        return `${base}/room/${roomId}`;\n    }, [\n        roomId\n    ]);\n    const clearMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        try {\n            sockRef.current?.emit(\"room:clear\", {\n                roomId\n            });\n        } catch (err) {\n            setError(\"Failed to clear messages\");\n            console.error(\"Clear messages error:\", err);\n        }\n    }, [\n        roomId\n    ]);\n    const manualReconnect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        try {\n            setError(null);\n            setConnectionStatus(\"connecting\");\n            sockRef.current?.disconnect();\n            sockRef.current?.connect();\n        } catch (err) {\n            setError(\"Failed to reconnect manually\");\n            console.error(\"Manual reconnect error:\", err);\n        }\n    }, []);\n    const dismissError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>setError(null), []);\n    const handleReaction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageId, emoji)=>{\n        try {\n            sockRef.current?.emit(\"room:reaction\", {\n                roomId,\n                messageId,\n                emoji,\n                name: \"Server\" // Server can react to messages\n            });\n        } catch (err) {\n            setError(\"Failed to send reaction\");\n            console.error(\"Reaction error:\", err);\n        }\n    }, [\n        roomId\n    ]);\n    // Memoize expensive calculations\n    const visible = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>participants.filter((p)=>(p?.name ?? \"\").trim() && p.name !== \"Server\"), [\n        participants\n    ]);\n    // Filter messages by selected participants and search query\n    const filteredMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>messages.filter((m)=>{\n            // Filter by participants\n            const participantMatch = selectedParticipants.size === 0 || selectedParticipants.has(m.name);\n            // Filter by search query\n            const searchMatch = !searchQuery || m.text.toLowerCase().includes(searchQuery.toLowerCase()) || m.name.toLowerCase().includes(searchQuery.toLowerCase());\n            return participantMatch && searchMatch;\n        }), [\n        messages,\n        selectedParticipants,\n        searchQuery\n    ]);\n    // Virtual scrolling calculations\n    const visibleMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (filteredMessages.length <= 50) {\n            // Don't use virtual scrolling for small lists\n            return filteredMessages;\n        }\n        const startIndex = Math.max(0, Math.floor(scrollTop / ITEM_HEIGHT) - BUFFER_SIZE);\n        const endIndex = Math.min(filteredMessages.length, Math.ceil((scrollTop + containerHeight) / ITEM_HEIGHT) + BUFFER_SIZE);\n        return filteredMessages.slice(startIndex, endIndex).map((msg, index)=>({\n                ...msg,\n                virtualIndex: startIndex + index\n            }));\n    }, [\n        filteredMessages,\n        scrollTop,\n        containerHeight,\n        ITEM_HEIGHT,\n        BUFFER_SIZE\n    ]);\n    // Get unique participant names from messages\n    const allParticipantNames = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Array.from(new Set(messages.map((m)=>m.name))).sort(), [\n        messages\n    ]);\n    // Calculate statistics\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const activeTime = Math.floor((Date.now() - roomStartTime) / 1000 / 60) // minutes\n        ;\n        return {\n            totalMessages: messages.length,\n            totalParticipants: visible.length,\n            activeTime,\n            messagesByParticipant: allParticipantNames.reduce((acc, name)=>{\n                acc[name] = messages.filter((m)=>m.name === name).length;\n                return acc;\n            }, {}),\n            averageMessageLength: messages.length > 0 ? Math.round(messages.reduce((sum, m)=>sum + m.text.length, 0) / messages.length) : 0,\n            messagesPerMinute: activeTime > 0 ? (messages.length / activeTime).toFixed(1) : \"0\"\n        };\n    }, [\n        messages,\n        visible.length,\n        allParticipantNames,\n        roomStartTime\n    ]);\n    const toggleParticipantFilter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((name)=>{\n        setSelectedParticipants((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(name)) {\n                newSet.delete(name);\n            } else {\n                newSet.add(name);\n            }\n            return newSet;\n        });\n    }, []);\n    const exportMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((format)=>{\n        const timestamp = new Date().toISOString().replace(/[:.]/g, \"-\");\n        const filename = `room-${roomId}-messages-${timestamp}.${format}`;\n        let content;\n        if (format === \"json\") {\n            content = JSON.stringify({\n                roomId,\n                exportedAt: new Date().toISOString(),\n                messageCount: messages.length,\n                messages: messages.map((m)=>({\n                        ...m,\n                        timestamp: new Date(m.ts).toISOString()\n                    }))\n            }, null, 2);\n        } else {\n            content = `Room: ${roomId}\\nExported: ${new Date().toLocaleString()}\\nMessages: ${messages.length}\\n\\n` + messages.map((m)=>`[${new Date(m.ts).toLocaleString()}] ${m.name}: ${m.text}`).join(\"\\n\");\n        }\n        const blob = new Blob([\n            content\n        ], {\n            type: format === \"json\" ? \"application/json\" : \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = filename;\n        a.click();\n        URL.revokeObjectURL(url);\n    }, [\n        roomId,\n        messages\n    ]);\n    // Keyboard shortcuts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyPress = (e)=>{\n            if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) return;\n            switch(e.key.toLowerCase()){\n                case \"f\":\n                    e.preventDefault();\n                    document.documentElement.requestFullscreen?.();\n                    break;\n                case \"c\":\n                    e.preventDefault();\n                    clearMessages();\n                    break;\n                case \"h\":\n                    e.preventDefault();\n                    setHideNames((v)=>!v);\n                    break;\n                case \"s\":\n                    e.preventDefault();\n                    setShowSettings((v)=>!v);\n                    break;\n                case \"escape\":\n                    e.preventDefault();\n                    setShowSettings(false);\n                    setError(null);\n                    break;\n            }\n        };\n        document.addEventListener(\"keydown\", handleKeyPress);\n        return ()=>document.removeEventListener(\"keydown\", handleKeyPress);\n    }, [\n        clearMessages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 select-none\",\n        role: \"main\",\n        \"aria-label\": \"Room server interface\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-500/20 border border-red-500/30 rounded-lg p-4 flex items-center justify-between\",\n                role: \"alert\",\n                \"aria-live\": \"polite\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-400\",\n                                \"aria-hidden\": \"true\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-red-200\",\n                                        children: \"Connection Error\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-red-300\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 15\n                                    }, this),\n                                    reconnectAttempts > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-red-400 mt-1\",\n                                        children: [\n                                            \"Reconnection attempts: \",\n                                            reconnectAttempts,\n                                            \"/5\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            connectionStatus === \"disconnected\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: manualReconnect,\n                                className: \"px-3 py-1 bg-red-500/30 hover:bg-red-500/40 rounded text-sm\",\n                                \"aria-label\": \"Retry connection\",\n                                children: \"Retry\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: dismissError,\n                                className: \"px-3 py-1 bg-white/10 hover:bg-white/20 rounded text-sm\",\n                                \"aria-label\": \"Dismiss error message\",\n                                children: \"Dismiss\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 500,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 483,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm uppercase tracking-[0.3em] opacity-80\",\n                                        children: \"Room\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `h-2 w-2 rounded-full ${connectionStatus === \"connected\" ? \"bg-green-400\" : connectionStatus === \"connecting\" ? \"bg-yellow-400 animate-pulse\" : \"bg-red-400\"}`,\n                                        title: `Connection: ${connectionStatus}`\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-black leading-none text-[clamp(2rem,10vw,8rem)]\",\n                                children: roomId\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"opacity-80\",\n                                children: \"Scan to join\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QR__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                text: joinUrl,\n                                size: 260\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs opacity-80 max-w-[260px] text-center break-all\",\n                                children: joinUrl\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 533,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 520,\n                columnNumber: 7\n            }, this),\n            !hideNames && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-[clamp(1rem,3vw,2rem)] font-semibold\",\n                                children: [\n                                    \"Participants (\",\n                                    visible.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowParticipantEvents(!showParticipantEvents),\n                                className: \"text-sm bg-white/10 hover:bg-white/20 px-3 py-1 rounded-lg\",\n                                children: showParticipantEvents ? \"Hide Activity\" : \"Show Activity\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 545,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"grid gap-3 [grid-template-columns:repeat(auto-fit,minmax(16ch,1fr))]\",\n                        children: visible.map((p)=>{\n                            const activity = participantActivity[p.name];\n                            const isActive = activity && Date.now() - activity.lastSeen < 60000 // Active within 1 minute\n                            ;\n                            const joinedAgo = activity ? Math.floor((Date.now() - activity.joinTime) / 1000 / 60) : 0;\n                            const lastSeenAgo = activity ? Math.floor((Date.now() - activity.lastSeen) / 1000 / 60) : 0;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"rounded-xl bg-white/10 px-4 py-3 text-[clamp(1rem,2.5vw,1.75rem)]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-2 h-2 rounded-full ${isActive ? \"bg-green-400\" : \"bg-gray-400\"}`\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: p.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 19\n                                    }, this),\n                                    showParticipantEvents && activity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-60 mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Joined: \",\n                                                    joinedAgo,\n                                                    \"m ago\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 23\n                                            }, this),\n                                            !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Last seen: \",\n                                                    lastSeenAgo,\n                                                    \"m ago\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, p.id, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 540,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-2xl bg-white/10 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowStats(!showStats),\n                        className: \"flex items-center justify-between w-full mb-3 text-[clamp(1rem,3vw,2rem)] font-semibold hover:opacity-80\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Room Statistics\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: showStats ? \"▼\" : \"▶\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 9\n                    }, this),\n                    showStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/5 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-400\",\n                                        children: stats.totalMessages\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm opacity-70\",\n                                        children: \"Messages\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 590,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/5 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-400\",\n                                        children: stats.totalParticipants\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm opacity-70\",\n                                        children: \"Participants\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/5 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-400\",\n                                        children: [\n                                            stats.activeTime,\n                                            \"m\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm opacity-70\",\n                                        children: \"Active Time\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/5 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-400\",\n                                        children: stats.messagesPerMinute\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm opacity-70\",\n                                        children: \"Msg/Min\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 602,\n                                columnNumber: 13\n                            }, this),\n                            Object.keys(stats.messagesByParticipant).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2 md:col-span-4 bg-white/5 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold mb-2\",\n                                        children: \"Messages by Participant:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n                                        children: Object.entries(stats.messagesByParticipant).sort(([, a], [, b])=>b - a).map(([name, count])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"truncate\",\n                                                        children: [\n                                                            name,\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-mono\",\n                                                        children: count\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, name, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 608,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2 md:col-span-4 bg-white/5 rounded-lg p-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold\",\n                                            children: \"Average message length:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" \",\n                                        stats.averageMessageLength,\n                                        \" characters\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 589,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 579,\n                columnNumber: 7\n            }, this),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-2xl bg-white/10 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-[clamp(1rem,3vw,2rem)] font-semibold\",\n                                children: \"Room Settings\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(false),\n                                className: \"text-sm opacity-70 hover:opacity-100\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 635,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-sm\",\n                                        children: \"Display Options\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"Sound Notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: settings.soundEnabled,\n                                                onChange: (e)=>{\n                                                    setSettings((prev)=>({\n                                                            ...prev,\n                                                            soundEnabled: e.target.checked\n                                                        }));\n                                                    setSoundEnabled(e.target.checked);\n                                                },\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"Auto Scroll\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: settings.autoScroll,\n                                                onChange: (e)=>{\n                                                    setSettings((prev)=>({\n                                                            ...prev,\n                                                            autoScroll: e.target.checked\n                                                        }));\n                                                    setAutoScroll(e.target.checked);\n                                                },\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"Show Timestamps\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: settings.showTimestamps,\n                                                onChange: (e)=>setSettings((prev)=>({\n                                                            ...prev,\n                                                            showTimestamps: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: \"Compact Mode\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: settings.compactMode,\n                                                onChange: (e)=>setSettings((prev)=>({\n                                                            ...prev,\n                                                            compactMode: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 641,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-sm\",\n                                        children: \"Performance\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: [\n                                                    \"Max Messages (\",\n                                                    settings.maxMessages,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"50\",\n                                                max: \"500\",\n                                                step: \"50\",\n                                                value: settings.maxMessages,\n                                                onChange: (e)=>setSettings((prev)=>({\n                                                            ...prev,\n                                                            maxMessages: parseInt(e.target.value)\n                                                        })),\n                                                className: \"w-full mt-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-xs opacity-60 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/5 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-semibold mb-2\",\n                                                children: \"Current Stats\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs space-y-1 opacity-80\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"Messages: \",\n                                                            messages.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"Participants: \",\n                                                            visible.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            \"Memory Usage: ~\",\n                                                            Math.round(JSON.stringify(messages).length / 1024),\n                                                            \"KB\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 634,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-2xl bg-white/10 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-[clamp(1rem,3vw,2rem)] font-semibold\",\n                                    children: \"Live Messages\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 726,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search messages...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"flex-1 px-3 py-2 text-sm bg-white/10 rounded-lg border border-white/20 focus:border-white/40 focus:outline-none\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 13\n                                    }, this),\n                                    searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSearchQuery(\"\"),\n                                        className: \"px-3 py-2 text-sm bg-white/10 hover:bg-white/20 rounded-lg\",\n                                        children: \"Clear\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 729,\n                                columnNumber: 11\n                            }, this),\n                            allParticipantNames.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm opacity-70\",\n                                        children: \"Filter by:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 748,\n                                        columnNumber: 15\n                                    }, this),\n                                    allParticipantNames.map((name)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleParticipantFilter(name),\n                                            className: `px-2 py-1 text-xs rounded-lg transition-colors ${selectedParticipants.has(name) ? \"bg-blue-500/30 text-blue-200\" : \"bg-white/10 hover:bg-white/20\"}`,\n                                            children: [\n                                                name,\n                                                \" \",\n                                                selectedParticipants.has(name) ? \"✓\" : \"\"\n                                            ]\n                                        }, name, true, {\n                                            fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                            lineNumber: 750,\n                                            columnNumber: 17\n                                        }, this)),\n                                    selectedParticipants.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedParticipants(new Set()),\n                                        className: \"px-2 py-1 text-xs rounded-lg bg-red-500/20 hover:bg-red-500/30\",\n                                        children: \"Clear filters\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 747,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>document.documentElement.requestFullscreen?.(),\n                                        className: \"rounded-lg bg-white/20 px-3 py-1 text-sm\",\n                                        children: \"Fullscreen (f)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: clearMessages,\n                                        className: \"rounded-lg bg-white/20 px-3 py-1 text-sm\",\n                                        children: \"Clear (c)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 774,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setHideNames((v)=>!v),\n                                        className: \"rounded-lg bg-white/20 px-3 py-1 text-sm\",\n                                        children: [\n                                            hideNames ? \"Show Names\" : \"Hide Names\",\n                                            \" (h)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSoundEnabled((v)=>!v),\n                                        className: `rounded-lg px-3 py-1 text-sm ${soundEnabled ? \"bg-green-500/20\" : \"bg-white/20\"}`,\n                                        children: soundEnabled ? \"\\uD83D\\uDD0A\" : \"\\uD83D\\uDD07\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 778,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setAutoScroll((v)=>!v),\n                                        className: `rounded-lg px-3 py-1 text-sm ${autoScroll ? \"bg-blue-500/20\" : \"bg-white/20\"}`,\n                                        children: autoScroll ? \"\\uD83D\\uDCDC\" : \"⏸️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"rounded-lg bg-white/20 px-3 py-1 text-sm\",\n                                                children: \"Export \\uD83D\\uDCE5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 785,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 top-full mt-1 hidden group-hover:block bg-black/80 rounded-lg p-2 space-y-1 min-w-[100px]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>exportMessages(\"txt\"),\n                                                        className: \"block w-full text-left px-2 py-1 text-sm hover:bg-white/20 rounded\",\n                                                        children: \"Text\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>exportMessages(\"json\"),\n                                                        className: \"block w-full text-left px-2 py-1 text-sm hover:bg-white/20 rounded\",\n                                                        children: \"JSON\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSettings(!showSettings),\n                                        className: \"rounded-lg bg-white/20 px-3 py-1 text-sm\",\n                                        children: \"Settings ⚙️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 772,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: scrollRef,\n                        className: \"flex max-h-[60vh] flex-col gap-6 overflow-y-auto pr-2\",\n                        onScroll: handleScroll,\n                        children: filteredMessages.length > 50 ? // Virtual scrolling for large lists\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                height: filteredMessages.length * ITEM_HEIGHT,\n                                position: \"relative\"\n                            },\n                            children: visibleMessages.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-[clamp(1rem,3.5vw,2.5rem)] absolute w-full\",\n                                    style: {\n                                        top: (m.virtualIndex || 0) * ITEM_HEIGHT,\n                                        height: ITEM_HEIGHT\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Message__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        msg: m,\n                                        isNew: newMessageIds.has(m.id),\n                                        searchQuery: searchQuery,\n                                        onReaction: handleReaction\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 809,\n                                        columnNumber: 19\n                                    }, this)\n                                }, m.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                    lineNumber: 801,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                            lineNumber: 799,\n                            columnNumber: 13\n                        }, this) : // Regular rendering for small lists\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                filteredMessages.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-[clamp(1rem,3.5vw,2.5rem)]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Message__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            msg: m,\n                                            isNew: newMessageIds.has(m.id),\n                                            searchQuery: searchQuery,\n                                            onReaction: handleReaction\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                            lineNumber: 823,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, m.id, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 17\n                                    }, this)),\n                                filteredMessages.length === 0 && messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center opacity-50 py-8\",\n                                    children: \"No messages from selected participants\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                    lineNumber: 832,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 796,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 724,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n        lineNumber: 480,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/server/[roomId]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Message.tsx":
/*!************************************!*\
  !*** ./src/components/Message.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction getRelativeTime(timestamp) {\n    const now = Date.now();\n    const diff = now - timestamp;\n    const seconds = Math.floor(diff / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n    const days = Math.floor(hours / 24);\n    if (seconds < 60) return \"just now\";\n    if (minutes < 60) return `${minutes}m ago`;\n    if (hours < 24) return `${hours}h ago`;\n    if (days < 7) return `${days}d ago`;\n    return new Date(timestamp).toLocaleDateString();\n}\nfunction highlightText(text, searchQuery) {\n    if (!searchQuery) return text;\n    const regex = new RegExp(`(${searchQuery.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\")})`, \"gi\");\n    const parts = text.split(regex);\n    return parts.map((part, index)=>regex.test(part) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mark\", {\n            className: \"bg-yellow-400/30 text-yellow-200 px-1 rounded\",\n            children: part\n        }, index, false, {\n            fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this) : part);\n}\nconst REACTION_EMOJIS = [\n    \"\\uD83D\\uDC4D\",\n    \"❤️\",\n    \"\\uD83D\\uDE02\",\n    \"\\uD83D\\uDE2E\",\n    \"\\uD83D\\uDE22\",\n    \"\\uD83D\\uDE21\"\n];\nconst Message = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(function Message({ msg, isNew = false, searchQuery = \"\", onReaction }) {\n    const date = new Date(msg.ts);\n    const relativeTime = getRelativeTime(msg.ts);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-xl bg-white/10 px-4 py-2 backdrop-blur-sm transition-all duration-500 group ${isNew ? \"animate-slide-in-left\" : \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs opacity-70 cursor-help\",\n                title: date.toLocaleString(),\n                children: relativeTime\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-semibold\",\n                children: highlightText(msg.name, searchQuery)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"opacity-90 mb-2\",\n                children: highlightText(msg.text, searchQuery)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 flex-wrap\",\n                children: [\n                    msg.reactions && Object.entries(msg.reactions).map(([emoji, users])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onReaction?.(msg.id, emoji),\n                            className: \"flex items-center gap-1 px-2 py-1 bg-white/10 hover:bg-white/20 rounded-full text-xs transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: emoji\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: users.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, emoji, true, {\n                            fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)),\n                    onReaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative opacity-0 group-hover:opacity-100 transition-opacity\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"px-2 py-1 bg-white/10 hover:bg-white/20 rounded-full text-xs\",\n                                children: \"+\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-full left-0 mb-1 hidden group-hover:block bg-black/80 rounded-lg p-2 flex gap-1\",\n                                children: REACTION_EMOJIS.map((emoji)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>onReaction(msg.id, emoji),\n                                        className: \"hover:bg-white/20 rounded p-1 text-sm\",\n                                        children: emoji\n                                    }, emoji, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Message);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Message.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/QR.tsx":
/*!*******************************!*\
  !*** ./src/components/QR.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QR)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var qrcode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! qrcode */ \"(ssr)/./node_modules/qrcode/lib/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction QR({ text, size = 256 }) {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!canvasRef.current) return;\n        qrcode__WEBPACK_IMPORTED_MODULE_1__.toCanvas(canvasRef.current, text, {\n            width: size,\n            margin: 1\n        });\n    }, [\n        text,\n        size\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        className: \"rounded-lg shadow-lg bg-white\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/QR.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9RUi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUMyQjtBQUNjO0FBRTFCLFNBQVNHLEdBQUcsRUFBRUMsSUFBSSxFQUFFQyxPQUFPLEdBQUcsRUFBbUM7SUFDOUUsTUFBTUMsWUFBWUosNkNBQU1BLENBQW9CO0lBQzVDRCxnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQ0ssVUFBVUMsT0FBTyxFQUFFO1FBQ3hCUCw0Q0FBZSxDQUFDTSxVQUFVQyxPQUFPLEVBQUVILE1BQU07WUFBRUssT0FBT0o7WUFBTUssUUFBUTtRQUFFO0lBQ3BFLEdBQUc7UUFBQ047UUFBTUM7S0FBSztJQUNmLHFCQUFPLDhEQUFDTTtRQUFPQyxLQUFLTjtRQUFXTyxXQUFVOzs7Ozs7QUFDM0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFsdGltZS1xci1yb29tLy4vc3JjL2NvbXBvbmVudHMvUVIudHN4P2E3NzciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5pbXBvcnQgUVJDb2RlIGZyb20gJ3FyY29kZSdcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFFSKHsgdGV4dCwgc2l6ZSA9IDI1NiB9OiB7IHRleHQ6IHN0cmluZzsgc2l6ZT86IG51bWJlciB9KSB7XG4gIGNvbnN0IGNhbnZhc1JlZiA9IHVzZVJlZjxIVE1MQ2FudmFzRWxlbWVudD4obnVsbClcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWNhbnZhc1JlZi5jdXJyZW50KSByZXR1cm5cbiAgICBRUkNvZGUudG9DYW52YXMoY2FudmFzUmVmLmN1cnJlbnQsIHRleHQsIHsgd2lkdGg6IHNpemUsIG1hcmdpbjogMSB9KVxuICB9LCBbdGV4dCwgc2l6ZV0pXG4gIHJldHVybiA8Y2FudmFzIHJlZj17Y2FudmFzUmVmfSBjbGFzc05hbWU9XCJyb3VuZGVkLWxnIHNoYWRvdy1sZyBiZy13aGl0ZVwiIC8+XG59Il0sIm5hbWVzIjpbIlFSQ29kZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsIlFSIiwidGV4dCIsInNpemUiLCJjYW52YXNSZWYiLCJjdXJyZW50IiwidG9DYW52YXMiLCJ3aWR0aCIsIm1hcmdpbiIsImNhbnZhcyIsInJlZiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/QR.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"de9c25289118\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmVhbHRpbWUtcXItcm9vbS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NDIyNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImRlOWMyNTI4OTExOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"Realtime QR Room\",\n    description: \"Server & participant views with Socket.IO\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-gradient-to-br from-brand-800 via-brand-600 to-brand-500 text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-6xl p-6\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/layout.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXNCO0FBR2YsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQUVDLFFBQVEsRUFBaUM7SUFDNUUscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVU7c0JBQ2QsNEVBQUNDO2dCQUFJRCxXQUFVOzBCQUF5Qko7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJaEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFsdGltZS1xci1yb29tLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnUmVhbHRpbWUgUVIgUm9vbScsXG4gIGRlc2NyaXB0aW9uOiAnU2VydmVyICYgcGFydGljaXBhbnQgdmlld3Mgd2l0aCBTb2NrZXQuSU8nXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYnJhbmQtODAwIHZpYS1icmFuZC02MDAgdG8tYnJhbmQtNTAwIHRleHQtd2hpdGVcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteC1hdXRvIG1heC13LTZ4bCBwLTZcIj57Y2hpbGRyZW59PC9kaXY+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59Il0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/server/[roomId]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/server/[roomId]/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/ws","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/debug","vendor-chunks/socket.io-parser","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/ms","vendor-chunks/qrcode","vendor-chunks/pngjs","vendor-chunks/dijkstrajs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fserver%2F%5BroomId%5D%2Fpage&page=%2Fserver%2F%5BroomId%5D%2Fpage&appPaths=%2Fserver%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Fserver%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();