/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/server/[roomId]/page";
exports.ids = ["app/server/[roomId]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?d272":
/*!********************************!*\
  !*** supports-color (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fserver%2F%5BroomId%5D%2Fpage&page=%2Fserver%2F%5BroomId%5D%2Fpage&appPaths=%2Fserver%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Fserver%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fserver%2F%5BroomId%5D%2Fpage&page=%2Fserver%2F%5BroomId%5D%2Fpage&appPaths=%2Fserver%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Fserver%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'server',\n        {\n        children: [\n        '[roomId]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/server/[roomId]/page.tsx */ \"(rsc)/./src/app/server/[roomId]/page.tsx\")), \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/server/[roomId]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/server/[roomId]/page\",\n        pathname: \"/server/[roomId]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fserver%2F%5BroomId%5D%2Fpage&page=%2Fserver%2F%5BroomId%5D%2Fpage&appPaths=%2Fserver%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Fserver%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Fserver%2F%5BroomId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Fserver%2F%5BroomId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/server/[roomId]/page.tsx */ \"(ssr)/./src/app/server/[roomId]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbnVsbCUyRkRvY3VtZW50cyUyRkRldmVsb3BtZW50JTJGU2VwdC0yMDI1JTJGcmVhbHRpbWUtcm9vbSUyRnNyYyUyRmFwcCUyRnNlcnZlciUyRiU1QnJvb21JZCU1RCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBK0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFsdGltZS1xci1yb29tLz8zYzQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL251bGwvRG9jdW1lbnRzL0RldmVsb3BtZW50L1NlcHQtMjAyNS9yZWFsdGltZS1yb29tL3NyYy9hcHAvc2VydmVyL1tyb29tSWRdL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Fserver%2F%5BroomId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/server/[roomId]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/server/[roomId]/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServerView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _components_QR__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/QR */ \"(ssr)/./src/components/QR.tsx\");\n/* harmony import */ var _components_Message__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Message */ \"(ssr)/./src/components/Message.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ServerView({ params }) {\n    const { roomId } = params;\n    const [participants, setParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [hideNames, setHideNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sockRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // init socket inside component to avoid stale global instance\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const s = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(window.location.origin, {\n            transports: [\n                \"websocket\"\n            ]\n        });\n        sockRef.current = s;\n        const join = ()=>s.emit(\"room:join\", {\n                roomId,\n                name: \"Server\"\n            });\n        s.on(\"connect\", join);\n        join() // first load\n        ;\n        s.on(\"room:state\", (state)=>{\n            setParticipants(state.participants);\n            setMessages(state.messages);\n        });\n        s.on(\"room:message\", (msg)=>setMessages((prev)=>[\n                    ...prev,\n                    msg\n                ]));\n        return ()=>{\n            s.off(\"connect\", join);\n            s.off(\"room:state\");\n            s.off(\"room:message\");\n            s.disconnect();\n        };\n    }, [\n        roomId\n    ]);\n    // auto-scroll on new messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const el = scrollRef.current;\n        if (el) el.scrollTop = el.scrollHeight;\n    }, [\n        messages\n    ]);\n    const joinUrl = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const base = process.env.NEXT_PUBLIC_BASE_URL || window.location.origin;\n        return `${base}/room/${roomId}`;\n    }, [\n        roomId\n    ]);\n    const clearMessages = ()=>sockRef.current?.emit(\"room:clear\", {\n            roomId\n        });\n    const visible = participants.filter((p)=>(p?.name ?? \"\").trim() && p.name !== \"Server\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 select-none\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm uppercase tracking-[0.3em] opacity-80\",\n                                children: \"Room\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-black leading-none text-[clamp(2rem,10vw,8rem)]\",\n                                children: roomId\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"opacity-80\",\n                                children: \"Scan to join\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QR__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                text: joinUrl,\n                                size: 260\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs opacity-80 max-w-[260px] text-center break-all\",\n                                children: joinUrl\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            !hideNames && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3 text-[clamp(1rem,3vw,2rem)] font-semibold\",\n                        children: [\n                            \"Participants (\",\n                            visible.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"grid gap-3 [grid-template-columns:repeat(auto-fit,minmax(12ch,1fr))]\",\n                        children: visible.map((p)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"rounded-xl bg-white/10 px-4 py-3 text-[clamp(1rem,2.5vw,1.75rem)]\",\n                                children: p.name\n                            }, p.id, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-2xl bg-white/10 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-[clamp(1rem,3vw,2rem)] font-semibold\",\n                                children: \"Live Messages\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>document.documentElement.requestFullscreen?.(),\n                                        className: \"rounded-lg bg-white/20 px-3 py-1 text-sm\",\n                                        children: \"Fullscreen (f)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: clearMessages,\n                                        className: \"rounded-lg bg-white/20 px-3 py-1 text-sm\",\n                                        children: \"Clear (c)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setHideNames((v)=>!v),\n                                        className: \"rounded-lg bg-white/20 px-3 py-1 text-sm\",\n                                        children: [\n                                            hideNames ? \"Show Names\" : \"Hide Names\",\n                                            \" (h)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: scrollRef,\n                        className: \"flex max-h-[60vh] flex-col gap-6 overflow-y-auto pr-2\",\n                        children: messages.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-[clamp(1rem,3.5vw,2.5rem)]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Message__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    msg: m\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            }, m.id, false, {\n                                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/server/[roomId]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Message.tsx":
/*!************************************!*\
  !*** ./src/components/Message.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Message)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Message({ msg }) {\n    const date = new Date(msg.ts);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-xl bg-white/10 px-4 py-2 backdrop-blur-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs opacity-70\",\n                children: date.toLocaleTimeString()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-semibold\",\n                children: msg.name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"opacity-90\",\n                children: msg.text\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Message.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9NZXNzYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRWUsU0FBU0EsUUFBUSxFQUFFQyxHQUFHLEVBQWM7SUFDakQsTUFBTUMsT0FBTyxJQUFJQyxLQUFLRixJQUFJRyxFQUFFO0lBQzVCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQXNCSixLQUFLSyxrQkFBa0I7Ozs7OzswQkFDNUQsOERBQUNGO2dCQUFJQyxXQUFVOzBCQUFpQkwsSUFBSU8sSUFBSTs7Ozs7OzBCQUN4Qyw4REFBQ0g7Z0JBQUlDLFdBQVU7MEJBQWNMLElBQUlRLElBQUk7Ozs7Ozs7Ozs7OztBQUczQyIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWx0aW1lLXFyLXJvb20vLi9zcmMvY29tcG9uZW50cy9NZXNzYWdlLnRzeD8wNWVlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE1lc3NhZ2UgYXMgTSB9IGZyb20gJ0AvbGliL3R5cGVzJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBNZXNzYWdlKHsgbXNnIH06IHsgbXNnOiBNIH0pIHtcbiAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKG1zZy50cylcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInJvdW5kZWQteGwgYmctd2hpdGUvMTAgcHgtNCBweS0yIGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBvcGFjaXR5LTcwXCI+e2RhdGUudG9Mb2NhbGVUaW1lU3RyaW5nKCl9PC9kaXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGRcIj57bXNnLm5hbWV9PC9kaXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm9wYWNpdHktOTBcIj57bXNnLnRleHR9PC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn0iXSwibmFtZXMiOlsiTWVzc2FnZSIsIm1zZyIsImRhdGUiLCJEYXRlIiwidHMiLCJkaXYiLCJjbGFzc05hbWUiLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJuYW1lIiwidGV4dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Message.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/QR.tsx":
/*!*******************************!*\
  !*** ./src/components/QR.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QR)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var qrcode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! qrcode */ \"(ssr)/./node_modules/qrcode/lib/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction QR({ text, size = 256 }) {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!canvasRef.current) return;\n        qrcode__WEBPACK_IMPORTED_MODULE_1__.toCanvas(canvasRef.current, text, {\n            width: size,\n            margin: 1\n        });\n    }, [\n        text,\n        size\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        className: \"rounded-lg shadow-lg bg-white\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/QR.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9RUi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUMyQjtBQUNjO0FBRTFCLFNBQVNHLEdBQUcsRUFBRUMsSUFBSSxFQUFFQyxPQUFPLEdBQUcsRUFBbUM7SUFDOUUsTUFBTUMsWUFBWUosNkNBQU1BLENBQW9CO0lBQzVDRCxnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQ0ssVUFBVUMsT0FBTyxFQUFFO1FBQ3hCUCw0Q0FBZSxDQUFDTSxVQUFVQyxPQUFPLEVBQUVILE1BQU07WUFBRUssT0FBT0o7WUFBTUssUUFBUTtRQUFFO0lBQ3BFLEdBQUc7UUFBQ047UUFBTUM7S0FBSztJQUNmLHFCQUFPLDhEQUFDTTtRQUFPQyxLQUFLTjtRQUFXTyxXQUFVOzs7Ozs7QUFDM0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFsdGltZS1xci1yb29tLy4vc3JjL2NvbXBvbmVudHMvUVIudHN4P2E3NzciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5pbXBvcnQgUVJDb2RlIGZyb20gJ3FyY29kZSdcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFFSKHsgdGV4dCwgc2l6ZSA9IDI1NiB9OiB7IHRleHQ6IHN0cmluZzsgc2l6ZT86IG51bWJlciB9KSB7XG4gIGNvbnN0IGNhbnZhc1JlZiA9IHVzZVJlZjxIVE1MQ2FudmFzRWxlbWVudD4obnVsbClcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWNhbnZhc1JlZi5jdXJyZW50KSByZXR1cm5cbiAgICBRUkNvZGUudG9DYW52YXMoY2FudmFzUmVmLmN1cnJlbnQsIHRleHQsIHsgd2lkdGg6IHNpemUsIG1hcmdpbjogMSB9KVxuICB9LCBbdGV4dCwgc2l6ZV0pXG4gIHJldHVybiA8Y2FudmFzIHJlZj17Y2FudmFzUmVmfSBjbGFzc05hbWU9XCJyb3VuZGVkLWxnIHNoYWRvdy1sZyBiZy13aGl0ZVwiIC8+XG59Il0sIm5hbWVzIjpbIlFSQ29kZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsIlFSIiwidGV4dCIsInNpemUiLCJjYW52YXNSZWYiLCJjdXJyZW50IiwidG9DYW52YXMiLCJ3aWR0aCIsIm1hcmdpbiIsImNhbnZhcyIsInJlZiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/QR.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"bd6a5f056296\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmVhbHRpbWUtcXItcm9vbS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NDIyNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImJkNmE1ZjA1NjI5NlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"Realtime QR Room\",\n    description: \"Server & participant views with Socket.IO\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-gradient-to-br from-brand-800 via-brand-600 to-brand-500 text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-6xl p-6\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/layout.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXNCO0FBR2YsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQUVDLFFBQVEsRUFBaUM7SUFDNUUscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVU7c0JBQ2QsNEVBQUNDO2dCQUFJRCxXQUFVOzBCQUF5Qko7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJaEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFsdGltZS1xci1yb29tLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnUmVhbHRpbWUgUVIgUm9vbScsXG4gIGRlc2NyaXB0aW9uOiAnU2VydmVyICYgcGFydGljaXBhbnQgdmlld3Mgd2l0aCBTb2NrZXQuSU8nXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYnJhbmQtODAwIHZpYS1icmFuZC02MDAgdG8tYnJhbmQtNTAwIHRleHQtd2hpdGVcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteC1hdXRvIG1heC13LTZ4bCBwLTZcIj57Y2hpbGRyZW59PC9kaXY+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59Il0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/server/[roomId]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/server/[roomId]/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/server/[roomId]/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/ws","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/debug","vendor-chunks/socket.io-parser","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/ms","vendor-chunks/qrcode","vendor-chunks/pngjs","vendor-chunks/dijkstrajs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fserver%2F%5BroomId%5D%2Fpage&page=%2Fserver%2F%5BroomId%5D%2Fpage&appPaths=%2Fserver%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Fserver%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();