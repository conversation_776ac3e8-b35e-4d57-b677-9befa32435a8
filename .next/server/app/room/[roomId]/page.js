/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/room/[roomId]/page";
exports.ids = ["app/room/[roomId]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?d272":
/*!********************************!*\
  !*** supports-color (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Froom%2F%5BroomId%5D%2Fpage&page=%2Froom%2F%5BroomId%5D%2Fpage&appPaths=%2Froom%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Froom%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Froom%2F%5BroomId%5D%2Fpage&page=%2Froom%2F%5BroomId%5D%2Fpage&appPaths=%2Froom%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Froom%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'room',\n        {\n        children: [\n        '[roomId]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/room/[roomId]/page.tsx */ \"(rsc)/./src/app/room/[roomId]/page.tsx\")), \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/room/[roomId]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/room/[roomId]/page\",\n        pathname: \"/room/[roomId]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Froom%2F%5BroomId%5D%2Fpage&page=%2Froom%2F%5BroomId%5D%2Fpage&appPaths=%2Froom%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Froom%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Froom%2F%5BroomId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Froom%2F%5BroomId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/room/[roomId]/page.tsx */ \"(ssr)/./src/app/room/[roomId]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbnVsbCUyRkRvY3VtZW50cyUyRkRldmVsb3BtZW50JTJGU2VwdC0yMDI1JTJGcmVhbHRpbWUtcm9vbSUyRnNyYyUyRmFwcCUyRnJvb20lMkYlNUJyb29tSWQlNUQlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEtBQTZIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmVhbHRpbWUtcXItcm9vbS8/M2NiMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9udWxsL0RvY3VtZW50cy9EZXZlbG9wbWVudC9TZXB0LTIwMjUvcmVhbHRpbWUtcm9vbS9zcmMvYXBwL3Jvb20vW3Jvb21JZF0vcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp%2Froom%2F%5BroomId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/room/[roomId]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/room/[roomId]/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoomView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Toast */ \"(ssr)/./src/components/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction RoomView({ params }) {\n    const { roomId } = params;\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [text, setText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [joined, setJoined] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sockRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const s = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(window.location.origin, {\n            transports: [\n                \"websocket\"\n            ]\n        });\n        sockRef.current = s;\n        const tryJoin = ()=>{\n            if (joined && name.trim()) s.emit(\"room:join\", {\n                roomId,\n                name: name.trim()\n            });\n        };\n        s.on(\"connect\", tryJoin);\n        tryJoin();\n        return ()=>{\n            s.off(\"connect\", tryJoin);\n            s.disconnect();\n        };\n    }, [\n        joined,\n        name,\n        roomId\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mx-auto max-w-md space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: [\n                    \"Room \",\n                    roomId\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            !joined ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"space-y-3 rounded-2xl bg-white/10 p-4\",\n                onSubmit: (e)=>{\n                    e.preventDefault();\n                    if (!name.trim()) return;\n                    setJoined(true);\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm opacity-80\",\n                        children: \"Display name\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        className: \"w-full rounded-lg bg-white/10 px-3 py-2 outline-none ring-1 ring-white/20\",\n                        value: name,\n                        onChange: (e)=>setName(e.target.value),\n                        placeholder: \"Your name\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full rounded-xl bg-brand-500 px-4 py-2 font-semibold hover:bg-brand-600\",\n                        children: \"Join\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"space-y-3 rounded-2xl bg-white/10 p-4\",\n                onSubmit: (e)=>{\n                    e.preventDefault();\n                    if (!text.trim()) return;\n                    sockRef.current?.emit(\"room:message\", {\n                        roomId,\n                        text,\n                        name: name.trim()\n                    });\n                    setText(\"\");\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm opacity-80\",\n                        children: \"Post a message\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        className: \"w-full rounded-lg bg-white/10 px-3 py-2 outline-none ring-1 ring-white/20\",\n                        value: text,\n                        onChange: (e)=>setText(e.target.value),\n                        placeholder: \"Say something…\",\n                        maxLength: 140\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full rounded-xl bg-white/20 px-4 py-2\",\n                        children: \"Send\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this),\n            joined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Toast__WEBPACK_IMPORTED_MODULE_3__.Toast, {\n                text: `Joined as ${name.trim()}`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n                lineNumber: 72,\n                columnNumber: 18\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/room/[roomId]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Toast.tsx":
/*!**********************************!*\
  !*** ./src/components/Toast.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Toast auto */ \n\nfunction Toast({ text }) {\n    const [show, setShow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const t = setTimeout(()=>setShow(false), 2500);\n        return ()=>clearTimeout(t);\n    }, []);\n    if (!show) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 left-1/2 -translate-x-1/2 rounded-xl bg-black/70 px-4 py-2 text-sm shadow-lg\",\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/components/Toast.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Ub2FzdC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQzJDO0FBRXBDLFNBQVNFLE1BQU0sRUFBRUMsSUFBSSxFQUFvQjtJQUM5QyxNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR0osK0NBQVFBLENBQUM7SUFDakNELGdEQUFTQSxDQUFDO1FBQ1IsTUFBTU0sSUFBSUMsV0FBVyxJQUFNRixRQUFRLFFBQVE7UUFDM0MsT0FBTyxJQUFNRyxhQUFhRjtJQUM1QixHQUFHLEVBQUU7SUFDTCxJQUFJLENBQUNGLE1BQU0sT0FBTztJQUNsQixxQkFDRSw4REFBQ0s7UUFBSUMsV0FBVTtrQkFDWlA7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmVhbHRpbWUtcXItcm9vbS8uL3NyYy9jb21wb25lbnRzL1RvYXN0LnRzeD9hNzUyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuXG5leHBvcnQgZnVuY3Rpb24gVG9hc3QoeyB0ZXh0IH06IHsgdGV4dDogc3RyaW5nIH0pIHtcbiAgY29uc3QgW3Nob3csIHNldFNob3ddID0gdXNlU3RhdGUodHJ1ZSlcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB0ID0gc2V0VGltZW91dCgoKSA9PiBzZXRTaG93KGZhbHNlKSwgMjUwMClcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHQpXG4gIH0sIFtdKVxuICBpZiAoIXNob3cpIHJldHVybiBudWxsXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBib3R0b20tNiBsZWZ0LTEvMiAtdHJhbnNsYXRlLXgtMS8yIHJvdW5kZWQteGwgYmctYmxhY2svNzAgcHgtNCBweS0yIHRleHQtc20gc2hhZG93LWxnXCI+XG4gICAgICB7dGV4dH1cbiAgICA8L2Rpdj5cbiAgKVxufSJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIlRvYXN0IiwidGV4dCIsInNob3ciLCJzZXRTaG93IiwidCIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Toast.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"de9c25289118\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmVhbHRpbWUtcXItcm9vbS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NDIyNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImRlOWMyNTI4OTExOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"Realtime QR Room\",\n    description: \"Server & participant views with Socket.IO\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-gradient-to-br from-brand-800 via-brand-600 to-brand-500 text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-6xl p-6\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/layout.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXNCO0FBR2YsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQUVDLFFBQVEsRUFBaUM7SUFDNUUscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVU7c0JBQ2QsNEVBQUNDO2dCQUFJRCxXQUFVOzBCQUF5Qko7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJaEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFsdGltZS1xci1yb29tLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnUmVhbHRpbWUgUVIgUm9vbScsXG4gIGRlc2NyaXB0aW9uOiAnU2VydmVyICYgcGFydGljaXBhbnQgdmlld3Mgd2l0aCBTb2NrZXQuSU8nXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYnJhbmQtODAwIHZpYS1icmFuZC02MDAgdG8tYnJhbmQtNTAwIHRleHQtd2hpdGVcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteC1hdXRvIG1heC13LTZ4bCBwLTZcIj57Y2hpbGRyZW59PC9kaXY+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59Il0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/room/[roomId]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/room/[roomId]/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Development/Sept-2025/realtime-room/src/app/room/[roomId]/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/ws","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/debug","vendor-chunks/socket.io-parser","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/ms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Froom%2F%5BroomId%5D%2Fpage&page=%2Froom%2F%5BroomId%5D%2Fpage&appPaths=%2Froom%2F%5BroomId%5D%2Fpage&pagePath=private-next-app-dir%2Froom%2F%5BroomId%5D%2Fpage.tsx&appDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fnull%2FDocuments%2FDevelopment%2FSept-2025%2Frealtime-room&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();