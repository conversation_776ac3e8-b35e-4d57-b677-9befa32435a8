# --- Build stage ---
FROM node:20-alpine AS builder
WORKDIR /app
COPY package.json package-lock.json* pnpm-lock.yaml* yarn.lock* ./
RUN npm ci || yarn install || pnpm install
COPY . .
# Build Next.js for production
RUN npm run build || yarn build || pnpm build

# --- Run stage ---
FROM node:20-alpine
WORKDIR /app
ENV NODE_ENV=production
# Copy everything (including .next and node_modules) from builder
COPY --from=builder /app .
EXPOSE 3000
# Use tsx loader to run the custom server in prod, which serves the built .next assets
CMD ["node", "--loader=tsx", "server.ts"]