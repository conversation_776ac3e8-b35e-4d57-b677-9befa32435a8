/* eslint-disable no-console */
import { createServer } from 'http'
import next from 'next'
import { Server as IOServer, Socket } from 'socket.io'

const dev = process.env.NODE_ENV !== 'production'
const port = parseInt(process.env.PORT || '3000', 10)
const app = next({ dev })
const handle = app.getRequestHandler()

type Participant = { id: string; name: string }
type Msg = {
  id: string;
  name: string;
  text: string;
  ts: number;
  reactions?: Record<string, string[]>; // emoji -> array of participant names
}
type Room = { participants: Record<string, Participant>; messages: Msg[] }
const rooms = new Map<string, Room>()

function ensureRoom(roomId: string): Room {
  if (!rooms.has(roomId)) rooms.set(roomId, { participants: {}, messages: [] })
  return rooms.get(roomId)!
}

app.prepare().then(() => {
  const httpServer = createServer((req, res) => handle(req, res))
  const io = new IOServer(httpServer, { cors: { origin: true, credentials: true } })

  io.on('connection', (socket: Socket) => {
    socket.on('room:join', ({ roomId, name }: { roomId: string; name: string }) => {
      const room = ensureRoom(roomId)
      room.participants[socket.id] = { id: socket.id, name }
      socket.join(roomId)
      io.to(roomId).emit('room:state', {
        participants: Object.values(room.participants),
        messages: room.messages
      })
    })

    socket.on('room:message', ({ roomId, text, name }: { roomId: string; text: string; name: string }) => {
      const room = ensureRoom(roomId)
      const msg = { id: `${Date.now()}-${Math.random().toString(36).slice(2)}`, name, text, ts: Date.now() }
      room.messages.push(msg)
      io.to(roomId).emit('room:message', msg)
    })

    socket.on('room:reaction', ({ roomId, messageId, emoji, name }: {
      roomId: string;
      messageId: string;
      emoji: string;
      name: string
    }) => {
      const room = ensureRoom(roomId)
      const message = room.messages.find(m => m.id === messageId)

      if (message) {
        if (!message.reactions) {
          message.reactions = {}
        }

        if (!message.reactions[emoji]) {
          message.reactions[emoji] = []
        }

        // Toggle reaction - remove if already exists, add if not
        const userIndex = message.reactions[emoji].indexOf(name)
        if (userIndex > -1) {
          message.reactions[emoji].splice(userIndex, 1)
          if (message.reactions[emoji].length === 0) {
            delete message.reactions[emoji]
          }
        } else {
          message.reactions[emoji].push(name)
        }

        // Broadcast the reaction update
        io.to(roomId).emit('room:reaction', { messageId, emoji, name })
      }
    })

    socket.on('room:clear', ({ roomId }: { roomId: string }) => {
      const room = ensureRoom(roomId)
      room.messages = []
      io.to(roomId).emit('room:state', {
        participants: Object.values(room.participants),
        messages: room.messages
      })
    })

    socket.on('disconnect', () => {
      for (const [roomId, room] of rooms) {
        if (room.participants[socket.id]) {
          delete room.participants[socket.id]
          io.to(roomId).emit('room:state', {
            participants: Object.values(room.participants),
            messages: room.messages
          })
        }
      }
    })
  })

  httpServer.listen(port, () => {
    console.log(`> Ready on http://localhost:${port} (dev=${dev})`)
  })
})