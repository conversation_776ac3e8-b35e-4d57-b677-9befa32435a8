{"name": "realtime-qr-room", "private": true, "scripts": {"dev": "tsx server.ts", "build": "next build", "start": "NODE_ENV=production tsx server.ts"}, "dependencies": {"autoprefixer": "^10.4.20", "clsx": "^2.1.1", "next": "^14.2.5", "postcss": "^8.4.47", "qrcode": "^1.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "tailwindcss": "^3.4.10", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.14.12", "@types/qrcode": "^1.5.5", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "tsx": "^4.19.2", "typescript": "^5.5.4"}}