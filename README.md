# Realtime QR Room

Two-view realtime app:
- **Server View (TV)** shows a QR code, participant list, and live messages.
- **Participant View (Phone)** scans the QR, enters a name, and posts messages.

## Quick start
```bash
npm i
npm run dev
# open http://localhost:3000
```

## Production
```bash
npm run build
npm start
# ensure NEXT_PUBLIC_BASE_URL is set to your public URL
```

## Docker
```bash
docker compose up --build -d
# open http://localhost:3000
```

## Notes
- Custom server (`server.ts`) starts Next and Socket.IO.
- In-memory room state (swap with Redis for scale).
- Set `NEXT_PUBLIC_BASE_URL` so QR codes point to the correct host.